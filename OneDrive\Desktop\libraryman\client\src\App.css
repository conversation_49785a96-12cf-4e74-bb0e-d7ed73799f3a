/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

/* Global Input Field Fixes - Override any dark theme styling */
input,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="search"],
input[type="date"],
input[type="tel"],
textarea,
select {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  font-family: inherit !important;
  transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
}

input:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

input::placeholder,
textarea::placeholder {
  color: #6b7280 !important;
  opacity: 1 !important;
}

/* Ensure select options are also properly styled */
select option {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Specific fixes for search boxes and form inputs throughout the app */
.search-input input,
.search-container input,
.form-group input,
.input-with-icon input,
.filters-section input,
.filters-section select,
.filter-group input,
.filter-group select,
.search-box input {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
}

.search-input input:focus,
.search-container input:focus,
.form-group input:focus,
.input-with-icon input:focus,
.filters-section input:focus,
.filters-section select:focus,
.filter-group input:focus,
.filter-group select:focus,
.search-box input:focus {
  outline: none !important;
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Fix for circulation history search input */
.circulation-history .search-input input {
  background-color: #ffffff !important;
  color: #000000 !important;
  padding-left: 40px !important;
}

/* Fix for fine management search inputs */
.fine-management .search-input input,
.payment-management .search-input input {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Fix for modal and form inputs */
.modal input,
.modal select,
.modal textarea {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #d1d5db !important;
}

.modal input:focus,
.modal select:focus,
.modal textarea:focus {
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

#root {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  position: relative;
}

.App {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* Loading */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #666;
}

/* Clean and Simple Login Styles */
.login-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  width: 100vw;
  background-color: #f8fafc;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Prevent body scrolling when login page is active */
body.login-active {
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

.login-card {
  background: #ffffff;
  padding: 2.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  border: 1px solid #e5e7eb;
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-icon {
  color: #4f46e5;
  margin-bottom: 1.5rem;
}

.login-header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #111827;
  letter-spacing: -0.025em;
}

.login-header p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

/* Enhanced input group with proper icon positioning */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Login form input styling - override global styles for login */
.login-form .input-group input {
  width: 100% !important;
  padding: 12px 12px 12px 40px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 1rem !important;
  background-color: #ffffff !important;
  color: #000000 !important;
  transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
}

.login-form .input-group input:focus {
  outline: none !important;
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.login-form .input-group input::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

.login-button {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 14px 16px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 0.5rem;
  width: 100%;
}

.login-button:hover:not(:disabled) {
  background: #4338ca;
}

.login-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef2f2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  border: 1px solid #fecaca;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.login-footer p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background: #f8f9fa;
  overflow-y: auto;
  height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: #2d3748;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.sidebar-logo {
  color: #667eea;
  margin-bottom: 0.5rem;
}

.sidebar-header h2 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #a0aec0;
}

.user-info small {
  display: block;
  font-size: 0.8rem;
  color: #718096;
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 1rem;
}

.nav-toggle {
  width: 100%;
  background: none;
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.nav-toggle:hover {
  background: #4a5568;
}

.nav-toggle.expanded {
  background: #4a5568;
}

.nav-toggle span {
  flex: 1;
  text-align: left;
}

.nav-submenu {
  background: #1a202c;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 2rem;
  color: #a0aec0;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.nav-link:hover {
  background: #2d3748;
  color: white;
}

.nav-link.active {
  background: #667eea;
  color: white;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #4a5568;
}

.logout-button {
  width: 100%;
  background: #e53e3e;
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.logout-button:hover {
  background: #c53030;
}

/* Page Content Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.page-header h1 {
  font-size: 1.8rem;
  color: #2d3748;
  margin: 0;
}

.page-header p {
  color: #718096;
  margin: 0.5rem 0 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-icon {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.btn-icon:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-icon.danger {
  color: #e53e3e;
  border-color: #fed7d7;
}

.btn-icon.danger:hover {
  background: #e53e3e;
  color: white;
  border-color: #e53e3e;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(229, 62, 62, 0.2);
}

/* Admin Dashboard Stats Grid */
.admin-home .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 0;
}

.admin-home .stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-home .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.admin-home .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 0 0 0 60px;
}

.admin-home .stat-card.blue { border-left-color: #3182ce; }
.admin-home .stat-card.green { border-left-color: #38a169; }
.admin-home .stat-card.purple { border-left-color: #805ad5; }
.admin-home .stat-card.orange { border-left-color: #dd6b20; }
.admin-home .stat-card.red { border-left-color: #e53e3e; }
.admin-home .stat-card.teal { border-left-color: #319795; }
.admin-home .stat-card.yellow { border-left-color: #d69e2e; }

.admin-home .stat-icon {
  padding: 1rem;
  border-radius: 12px;
  background: #f7fafc;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.admin-home .stat-card.blue .stat-icon { background: #ebf8ff; color: #3182ce; }
.admin-home .stat-card.green .stat-icon { background: #f0fff4; color: #38a169; }
.admin-home .stat-card.purple .stat-icon { background: #faf5ff; color: #805ad5; }
.admin-home .stat-card.orange .stat-icon { background: #fffaf0; color: #dd6b20; }
.admin-home .stat-card.red .stat-icon { background: #fed7d7; color: #e53e3e; }
.admin-home .stat-card.teal .stat-icon { background: #e6fffa; color: #319795; }
.admin-home .stat-card.yellow .stat-icon { background: #fef5e7; color: #d69e2e; }

.admin-home .stat-content {
  flex: 1;
  min-width: 0;
}

.admin-home .stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #1a202c;
  line-height: 1.2;
}

.admin-home .stat-title {
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  line-height: 1.3;
}

.admin-home .stat-description {
  color: #718096;
  font-size: 0.8rem;
  line-height: 1.4;
  margin: 0;
}

/* Admin Dashboard Responsive Design */
@media (max-width: 1200px) {
  .admin-home .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }

  .admin-home .stat-card {
    padding: 1.25rem;
  }

  .admin-home .stat-icon {
    width: 50px;
    height: 50px;
    padding: 0.75rem;
  }

  .admin-home .stat-content h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .admin-home .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }

  .admin-home .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .admin-home .stat-icon {
    width: 48px;
    height: 48px;
  }

  .admin-home .stat-content h3 {
    font-size: 1.5rem;
  }

  .admin-home .stat-title {
    font-size: 0.9rem;
  }

  .admin-home .stat-description {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .admin-home .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admin-home .stat-card {
    padding: 0.75rem;
  }

  .admin-home .stat-content h3 {
    font-size: 1.25rem;
  }
}

/* Table Styles */
.students-table, .books-table, .circulation-table, .colleges-table, .departments-table, .librarians-table, .ebooks-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.students-table table, .books-table table, .circulation-table table, .colleges-table table, .departments-table table, .librarians-table table, .ebooks-table table {
  width: 100%;
  border-collapse: collapse;
}

.students-table th, .books-table th, .circulation-table th, .colleges-table th, .departments-table th, .librarians-table th, .ebooks-table th {
  background: #f7fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.students-table td, .books-table td, .circulation-table td, .colleges-table td, .departments-table td, .librarians-table td, .ebooks-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.students-table tr:hover, .books-table tr:hover, .circulation-table tr:hover, .colleges-table tr:hover, .departments-table tr:hover, .librarians-table tr:hover, .ebooks-table tr:hover {
  background: #f7fafc;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #c6f6d5;
  color: #22543d;
}

.status.inactive {
  background: #fed7d7;
  color: #742a2a;
}

.actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

/* Filters */
.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 400px;
}

.search-box svg {
  position: absolute;
  left: 12px;
  color: #718096;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  min-width: 200px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form Actions Styles */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.form-actions .btn {
  min-width: 120px;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  color: #2d3748;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #718096;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #000000 !important; /* Force black text color */
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  color: #000000 !important; /* Force black text color on focus */
}

/* Comprehensive select box text color fixes */
.form-group select,
.filters-section select,
select {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.form-group select:focus,
.filters-section select:focus,
select:focus {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.form-group select option,
.filters-section select option,
select option {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* Admin component specific select fixes */
.admin-dashboard select,
.fine-management select,
.counter-reports select,
.gate-reports select,
.manage-department select,
.payment-management select {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.admin-dashboard select option,
.fine-management select option,
.counter-reports select option,
.gate-reports select option,
.manage-department select option,
.payment-management select option {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* Report filter specific styles */
.filters-section select,
.filter-group select,
.filters select {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.filters-section select option,
.filter-group select option,
.filters select option {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* Component-specific select fixes */
.counter-reports select,
.fine-reports select,
.gate-entry-reports select,
.manage-department select,
.payment-management select,
.fine-management select {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.counter-reports select option,
.fine-reports select option,
.gate-entry-reports select option,
.manage-department select option,
.payment-management select option,
.fine-management select option {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* Ensure focus states also have black text */
.filters-section select:focus,
.filter-group select:focus,
.filters select:focus,
.counter-reports select:focus,
.fine-reports select:focus,
.gate-entry-reports select:focus,
.manage-department select:focus,
.payment-management select:focus,
.fine-management select:focus {
  color: #000000 !important;
  background-color: #ffffff !important;
}

.form-group small {
  color: #718096;
  font-size: 0.8rem;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-text {
  color: #ef4444;
  font-size: 12px;
  margin-top: 0.25rem;
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 2.5rem;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #374151;
}

/* Issue Book Styles */
.issue-book-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.issue-form-section {
  display: flex;
  flex-direction: column;
}

.form-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-card h2 {
  margin-bottom: 1.5rem;
  color: #1a202c;
}

/* Enhanced search input with proper icon positioning */
.search-input {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 2;
  pointer-events: none;
}

.search-input input {
  padding-left: 40px !important;
  width: 100%;
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Autocomplete Suggestions Styles */
.search-input-container {
  position: relative;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestion-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item .book-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.suggestion-item .book-details {
  font-size: 0.85rem;
  color: #666;
}

.suggestion-item .unavailable {
  font-size: 0.8rem;
  color: #dc3545;
  margin-top: 2px;
}

.search-loading {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8rem;
  color: #666;
  pointer-events: none;
}

.no-results {
  padding: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.date-input {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input svg {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  z-index: 1;
}

.date-input input {
  padding-left: 2.5rem;
  width: 100%;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.search-result-item {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f7fafc;
}

.search-result-item:hover {
  background: #f7fafc;
}

.search-result-item:last-child {
  border-bottom: none;
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.book-info strong {
  color: #1a202c;
}

.book-info span {
  color: #4a5568;
  font-size: 0.9rem;
}

.book-info small {
  color: #718096;
  font-size: 0.8rem;
}

.loading-spinner {
  position: absolute;
  right: 0.75rem;
  color: #667eea;
  font-size: 0.8rem;
}

/* User Info Panel Styles */
.user-info-section {
  display: flex;
  flex-direction: column;
}

.user-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.user-avatar {
  background: #667eea;
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
}

.user-details p {
  margin: 0.125rem 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.user-status {
  display: flex;
  align-items: center;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.success {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.danger {
  background: #fed7d7;
  color: #c53030;
}

.fine-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fed7d7;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.section {
  margin-bottom: 1.5rem;
}

.section h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-size: 1rem;
}

.no-data {
  color: #718096;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.books-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.book-item, .history-item {
  background: #f7fafc;
  border-radius: 6px;
  padding: 1rem;
  border-left: 4px solid #e2e8f0;
}

.book-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.book-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.book-details strong {
  color: #1a202c;
  font-size: 0.9rem;
}

.book-details span {
  color: #4a5568;
  font-size: 0.8rem;
}

.book-details small {
  color: #718096;
  font-size: 0.75rem;
}

.book-dates {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.date-info span {
  font-size: 0.75rem;
  color: #718096;
}

.overdue-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.fine-amount {
  background: #c53030;
  color: white;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  margin-left: 0.25rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-dates {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.history-dates span {
  font-size: 0.75rem;
  color: #718096;
}

.status {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status.returned {
  background: #c6f6d5;
  color: #22543d;
}

.status.overdue {
  background: #fed7d7;
  color: #c53030;
}

.fine {
  color: #c53030;
  font-weight: 500;
}

/* Responsive Design for Issue Book */
@media (max-width: 768px) {
  .issue-book-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .user-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .book-item {
    flex-direction: column;
    gap: 0.75rem;
  }

  .book-dates {
    align-items: flex-start;
  }
}

/* Return Book Styles */
.return-book-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.return-form-section {
  display: flex;
  flex-direction: column;
}

.books-selection-section {
  margin-top: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  color: #1a202c;
}

.books-grid {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.book-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.book-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.book-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.book-card.overdue {
  border-left: 4px solid #e53e3e;
}

.book-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.book-checkbox input {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-badge.overdue {
  background: #fed7d7;
  color: #c53030;
}

.status-badge.on-time {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.info {
  background: #bee3f8;
  color: #2c5282;
}

.book-details h4 {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
  font-size: 0.9rem;
}

.book-details p {
  margin: 0 0 0.25rem 0;
  color: #4a5568;
  font-size: 0.8rem;
}

.book-details small {
  color: #718096;
  font-size: 0.75rem;
}

.book-dates {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e2e8f0;
}

.date-row {
  margin-bottom: 0.25rem;
}

.date-row span {
  font-size: 0.75rem;
  color: #718096;
}

.overdue-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #fed7d7;
  border-radius: 4px;
}

.days-overdue {
  font-size: 0.75rem;
  color: #c53030;
  font-weight: 500;
}

.fine-amount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #c53030;
  font-weight: 600;
}

.fine-summary {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.fine-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #c53030;
}

.fine-header h3 {
  margin: 0;
  font-size: 1rem;
}

.fine-details p {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
}

.fine-details strong {
  color: #c53030;
  font-size: 1.1rem;
}

.fine-details small {
  color: #718096;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 1.5rem 0;
}

.stat-card {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  background: #667eea;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon.overdue {
  background: #e53e3e;
}

.stat-icon.fine {
  background: #d69e2e;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
}

.stat-label {
  font-size: 0.75rem;
  color: #718096;
}

.no-books-message {
  text-align: center;
  padding: 2rem;
  color: #718096;
}

.no-books-message svg {
  color: #48bb78;
  margin-bottom: 1rem;
}

.no-books-message h3 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.no-books-message p {
  margin: 0;
}

/* Responsive Design for Return Book */
@media (max-width: 768px) {
  .return-book-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .overdue-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Fine Management Styles */
.fine-management {
  padding: 2rem;
}

.fines-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.fines-table table {
  width: 100%;
  border-collapse: collapse;
}

.fines-table th {
  background: #f7fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.fines-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.fines-table tr:hover {
  background: #f7fafc;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-info strong {
  color: #1a202c;
  font-size: 0.9rem;
}

.user-info small {
  color: #718096;
  font-size: 0.75rem;
}

.amount {
  font-weight: 600;
  color: #d69e2e;
  font-size: 1rem;
}

.reason {
  color: #4a5568;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  width: fit-content;
}

.status.paid {
  background: #c6f6d5;
  color: #22543d;
}

.status.pending {
  background: #fed7d7;
  color: #c53030;
}

.btn-icon.success {
  color: #22543d;
  border-color: #c6f6d5;
}

.btn-icon.success:hover {
  background: #22543d;
  color: white;
  border-color: #22543d;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon svg {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  z-index: 1;
}

.input-with-icon input {
  padding-left: 2.5rem;
  width: 100%;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Filter Styles */
.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.filter-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive Design for Fine Management */
@media (max-width: 768px) {
  .fine-management {
    padding: 1rem;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .fines-table {
    overflow-x: auto;
  }

  .fines-table table {
    min-width: 600px;
  }

  .reason {
    max-width: 150px;
  }
}

/* Payment Management Styles */
.payment-management {
  padding: 2rem;
}

.payment-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.payment-form-section {
  display: flex;
  flex-direction: column;
}

.fines-selection-section {
  margin-top: 1.5rem;
}

.fines-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.fine-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.fine-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fine-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.fine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.fine-checkbox input {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.fine-amount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  color: #d69e2e;
  font-size: 1.1rem;
}

.fine-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #4a5568;
}

.payment-summary {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #0369a1;
}

.summary-header h3 {
  margin: 0;
  font-size: 1rem;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.summary-row.total {
  border-top: 1px solid #bae6fd;
  padding-top: 0.75rem;
  margin-top: 0.5rem;
  font-weight: 600;
  font-size: 1.1rem;
  color: #0369a1;
}

.no-fines-message {
  text-align: center;
  padding: 2rem;
  color: #718096;
}

.no-fines-message svg {
  color: #48bb78;
  margin-bottom: 1rem;
}

.no-fines-message h3 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.no-fines-message p {
  margin: 0;
}

.payment-success {
  background: #f0fff4;
  border: 1px solid #9ae6b4;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.success-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #22543d;
}

.success-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.success-details {
  margin-bottom: 1rem;
}

.success-details p {
  margin: 0.25rem 0;
  color: #22543d;
  font-size: 0.9rem;
}

.success-actions {
  display: flex;
  gap: 0.5rem;
}

.fine-stats {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.fine-stats h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 6px;
}

.stat-label {
  font-size: 0.8rem;
  color: #718096;
}

.stat-value {
  font-weight: 600;
  color: #1a202c;
}

/* Responsive Design for Payment Management */
@media (max-width: 768px) {
  .payment-management {
    padding: 1rem;
  }

  .payment-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .summary-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Librarian Note Styles */
.librarian-note {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 1rem;
  color: #0369a1;
}

.librarian-note p {
  margin: 0;
  font-size: 0.9rem;
  font-style: italic;
}

/* Student Profile Styles */
.student-profile {
  padding: 2rem;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.profile-info-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.student-avatar {
  background: #667eea;
  color: white;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-details {
  flex: 1;
}

.student-details h2 {
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.student-details p {
  margin: 0.25rem 0;
  color: #4a5568;
}

.profile-stats {
  display: flex;
  gap: 1rem;
}

.profile-tabs {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab {
  flex: 1;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #4a5568;
  font-weight: 500;
}

.tab:hover {
  background: #f7fafc;
}

.tab.active {
  background: #667eea;
  color: white;
}

.tab-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #718096;
}

.no-data svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.book-card {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #e2e8f0;
}

.book-card.overdue {
  border-left-color: #e53e3e;
  background: #fed7d7;
}

.book-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.book-header h4 {
  margin: 0;
  color: #1a202c;
  flex: 1;
}

.overdue-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: #c53030;
  font-size: 0.8rem;
  font-weight: 500;
}

.fine-amount {
  background: #c53030;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
}

.history-table, .fines-table {
  width: 100%;
  overflow-x: auto;
}

.history-table table, .fines-table table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th, .fines-table th,
.history-table td, .fines-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.history-table th, .fines-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #4a5568;
}

.btn-icon.info {
  color: #3182ce;
  border-color: #bee3f8;
}

.btn-icon.info:hover {
  background: #3182ce;
  color: white;
  border-color: #3182ce;
}

/* Settings Styles */
.settings {
  padding: 2rem;
}

.settings-container {
  max-width: 1000px;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  color: #1a202c;
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-item label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.setting-item input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.setting-item input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-item small {
  color: #6b7280;
  font-size: 0.8rem;
}

.settings-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
}

.summary-label {
  font-weight: 500;
  color: #374151;
}

.summary-value {
  font-weight: 600;
  color: #1f2937;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.settings-message {
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
}

.settings-message.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.settings-message.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
  .settings {
    padding: 1rem;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Gate Entry Management Styles */
.gate-entry-management {
  padding: 2rem;
}

.credentials-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.credentials-table table {
  width: 100%;
  border-collapse: collapse;
}

.credentials-table th {
  background: #f7fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.credentials-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.credentials-table tr:hover {
  background: #f7fafc;
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
}

.status.active {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.inactive {
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.info-section {
  margin-top: 2rem;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #1a202c;
}

.info-header h3 {
  margin: 0;
}

.info-content ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.info-content li {
  margin: 0.5rem 0;
  color: #4a5568;
}

/* Gate Entry Dashboard Styles */
.gate-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Enhanced Gate Entry Dashboard */
.gate-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.dashboard-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  color: #2d3748;
  font-size: 1.8rem;
}

.header-left p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.status-indicators {
  display: flex;
  gap: 15px;
  margin-right: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-item.connected {
  background: #e6fffa;
  color: #065f46;
  border: 1px solid #10b981;
}

.status-item.disconnected {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

/* Connection Alerts */
.connection-alerts {
  margin-bottom: 20px;
}

.alert {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.alert-error {
  background: #fef2f2;
  border: 1px solid #ef4444;
  color: #991b1b;
}

.alert-warning {
  background: #fffbeb;
  border: 1px solid #f59e0b;
  color: #92400e;
}

.alert h3 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.alert p {
  margin: 0;
  font-size: 0.9rem;
}

/* Scanning Area */
.scanning-area {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.scan-status-section {
  text-align: center;
  margin-bottom: 30px;
}

.scan-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.scan-light {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.scan-light.ready {
  background: #10b981;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.scan-light.scanning {
  background: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  animation: pulse 1.5s infinite;
}

.scan-light.error {
  background: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.scan-light-inner {
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  opacity: 0.9;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.scan-buffer {
  background: #f7fafc;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-family: monospace;
  color: #4a5568;
}

/* Enhanced Scan Result */
.scan-result-enhanced {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 5px solid;
  animation: slideIn 0.3s ease-out;
}

.scan-result-enhanced.success {
  border-left-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.scan-result-enhanced.error {
  border-left-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.result-icon {
  color: inherit;
}

.scan-result-enhanced.success .result-icon {
  color: #10b981;
}

.scan-result-enhanced.error .result-icon {
  color: #ef4444;
}

.result-status h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.timestamp {
  margin: 5px 0 0 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.student-details {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.student-photo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #e2e8f0;
}

.student-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  color: #2d3748;
}

.student-info p {
  margin: 2px 0;
  font-size: 0.9rem;
  color: #4a5568;
}

.error-details {
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.error-details h3 {
  margin: 0 0 10px 0;
  color: #991b1b;
}

.error-details p {
  margin: 5px 0;
  color: #4a5568;
}

.error-details small {
  color: #718096;
  font-family: monospace;
}

/* Payment Management Receipt Styles */
.payment-success {
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
  border: 1px solid #10b981;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.success-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  color: #065f46;
}

.success-header h3 {
  margin: 0;
  color: #065f46;
}

.success-details {
  margin: 15px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
}

.success-details p {
  margin: 5px 0;
  color: #374151;
}

.success-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.success-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

.receipt-note {
  margin-top: 15px;
  padding: 10px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
}

.receipt-note p {
  margin: 0;
  font-size: 0.9rem;
}

/* Circulation History Styles */
.circulation-history {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #2d3748;
  font-size: 1.8rem;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #718096;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Statistics Section */
.statistics-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e2e8f0;
  color: #4a5568;
}

.stat-icon.active {
  background: #bee3f8;
  color: #2b6cb0;
}

.stat-icon.overdue {
  background: #fed7d7;
  color: #c53030;
}

.stat-icon.fine {
  background: #fef5e7;
  color: #d69e2e;
}

.stat-icon.users {
  background: #e6fffa;
  color: #319795;
}

.stat-content h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.stat-content p {
  margin: 5px 0 0 0;
  color: #718096;
  font-size: 0.9rem;
}

/* Filters Section */
.filters-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
}

/* This duplicate search-input styling is now consolidated above */

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #000000 !important;
  background-color: #ffffff !important;
}

.date-range {
  display: flex;
  gap: 10px;
}

.date-range input {
  flex: 1;
}

/* Table Section */
.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.table-header h3 {
  margin: 0;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 10px;
}

.record-count {
  font-size: 0.9rem;
  color: #718096;
  font-weight: normal;
}

.loading-state {
  padding: 60px 20px;
  text-align: center;
  color: #718096;
}

.loading-state .spinning {
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced table container with better responsive design */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  margin-bottom: 20px;
}

/* Responsive table improvements */
@media (max-width: 1024px) {
  .circulation-table th,
  .circulation-table td {
    padding: 12px 10px;
    font-size: 0.8125rem;
  }

  .user-info, .book-info {
    gap: 2px;
  }

  .user-name, .book-title {
    font-size: 0.8125rem;
  }

  .user-id, .book-author, .book-isbn {
    font-size: 0.6875rem;
  }
}

@media (max-width: 768px) {
  .circulation-table th,
  .circulation-table td {
    padding: 10px 8px;
    font-size: 0.75rem;
  }

  .circulation-table th {
    font-size: 0.75rem;
  }

  .user-name, .book-title {
    font-size: 0.75rem;
  }

  .user-id, .book-author, .book-isbn {
    font-size: 0.625rem;
  }

  .user-type {
    font-size: 0.625rem;
    padding: 1px 4px;
  }
}

/* Enhanced Circulation History Table Styling */
.circulation-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.circulation-table th {
  background: #f8fafc;
  padding: 16px 14px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  z-index: 10;
}

.circulation-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  position: relative;
  padding-right: 30px;
}

.circulation-table th.sortable:hover {
  background: #f1f5f9;
}

.circulation-table th.sortable svg {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.circulation-table td {
  padding: 16px 14px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
  font-size: 0.875rem;
  line-height: 1.5;
}

.circulation-table tr:hover {
  background: #f9fafb;
}

.circulation-table tr:last-child td {
  border-bottom: none;
}

/* Enhanced table cell content styling */
.user-info, .book-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.user-name, .book-title {
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.25;
  word-wrap: break-word;
}

.user-id, .book-author, .book-isbn {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.2;
}

.user-type {
  font-size: 0.6875rem;
  color: #374151;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.due-date.overdue {
  color: #dc2626;
  font-weight: 500;
}

.overdue-days {
  font-size: 0.6875rem;
  color: #dc2626;
  font-weight: 600;
  background: #fef2f2;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
  display: inline-block;
}

/* Additional responsive improvements for login page */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 10px;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .login-form .input-group input {
    padding: 10px 10px 10px 36px !important;
    font-size: 0.9rem !important;
  }

  .input-icon,
  .search-input svg {
    left: 10px;
  }

  .login-form .input-group input,
  .search-input input {
    padding-left: 36px !important;
  }
}

/* Ensure consistent button styling across the app */
.btn, button {
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn:focus, button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Improve status badge styling */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Ensure proper text contrast for accessibility */
.status-badge.blue {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.green {
  background: #dcfce7;
  color: #166534;
}

.status-badge.red {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.orange {
  background: #fed7aa;
  color: #ea580c;
}

/* Issue Book Component Styling */
.restriction-note {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: normal;
  font-style: italic;
}

.help-text {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 4px;
  line-height: 1.3;
}

.date-input {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  z-index: 2;
  pointer-events: none;
}

.date-input input {
  padding-left: 40px !important;
  width: 100%;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.date-input input:disabled {
  background-color: #f5f5f5 !important;
  color: #666 !important;
  cursor: not-allowed !important;
  border-color: #d1d5db !important;
}

/* Form grid for side-by-side inputs */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Dashboard Link Styling */
.dashboard-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin-bottom: 10px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.dashboard-link:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3) !important;
}

.dashboard-link.active {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4) !important;
}

.dashboard-link svg {
  color: white !important;
}

/* Ensure proper spacing for dashboard section */
.nav-section:has(.dashboard-link) {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

/* Enhanced navigation spacing and visual hierarchy */
.sidebar-nav .nav-section:first-child {
  margin-top: 0;
}

.sidebar-nav .nav-section {
  margin-bottom: 8px;
}

/* Dashboard link specific enhancements */
.dashboard-link {
  position: relative;
  overflow: hidden;
}

.dashboard-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.dashboard-link:hover::before {
  left: 100%;
}

/* Improve nav-toggle button spacing */
.nav-toggle {
  margin-bottom: 5px;
}

/* Better visual separation between sections */
.nav-section + .nav-section {
  margin-top: 12px;
}

/* Enhanced Button Styling */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1.25;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
  background: #dc2626;
  color: white;
}

.btn-secondary:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* Header Actions Spacing */
.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions .btn {
  white-space: nowrap;
}

/* Modal Actions Styling */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.modal-actions .btn {
  min-width: 100px;
}

/* Action Button Enhancements for Dashboard */
.action-button {
  width: 100%;
  justify-content: center;
  margin-top: 16px;
  padding: 12px 20px;
  font-weight: 600;
}

.action-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.action-card:hover .action-button {
  transform: none; /* Prevent double transform */
}

/* Responsive Design for Header Actions */
@media (max-width: 768px) {
  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .header-actions .btn {
    font-size: 0.8125rem;
    padding: 8px 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-header .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .modal-actions .btn {
    width: 100%;
  }
}

/* Category Management Specific Styling */
.btn-success svg {
  color: white;
}

/* Ensure proper button hierarchy */
.btn-primary {
  order: 1;
}

.btn-success {
  order: 2;
}

.btn-secondary {
  order: 3;
}

/* Category Dropdown Enhancements */
.select-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.select-wrapper select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.select-wrapper select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.select-wrapper select:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
  opacity: 0.7;
}

.loading-indicator {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
  border: 1px solid #4f46e5;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 8px 12px;
  font-size: 0.75rem;
  color: #4f46e5;
  z-index: 10;
  animation: fadeIn 0.3s ease;
}

.loading-indicator span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-indicator span::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #4f46e5;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced Help Text */
.help-text {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 4px;
  line-height: 1.3;
  font-style: italic;
}

/* Form Group Spacing */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

/* Select Dropdown Styling */
select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

select:disabled {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Link Button Styling */
.link-button {
  background: none;
  border: none;
  color: #4f46e5;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0 0 0 4px;
  transition: color 0.2s ease;
}

.link-button:hover {
  color: #3730a3;
  text-decoration: none;
}

.link-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  border-radius: 2px;
}

/* Enhanced Help Text with Link Buttons */
.help-text {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 4px;
  line-height: 1.4;
  font-style: italic;
}

.help-text .link-button {
  font-style: normal;
  font-weight: 500;
}

/* Form Field Indicators */
.required {
  color: #dc2626;
  font-weight: 600;
  font-size: 0.875rem;
}

.optional {
  color: #6b7280;
  font-weight: 400;
  font-size: 0.875rem;
  font-style: italic;
}

/* Help Text for Forms */
.help-text {
  color: #6b7280;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  line-height: 1.4;
}

.copies-info {
  color: #059669;
  font-weight: 500;
}

/* Delete All Books Button */
.btn-danger {
  background: #dc2626;
  color: white;
  border: 1px solid #dc2626;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.btn-danger:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

/* Warning styling for dangerous actions */
.btn-danger[title*="Delete all"] {
  position: relative;
}

.btn-danger[title*="Delete all"]:hover::after {
  content: "⚠️ Dangerous Action";
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: #fef2f2;
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
  border: 1px solid #fecaca;
  z-index: 1000;
}

/* Book Table Field Styling */
.edition-badge {
  display: inline-block;
  background: #e0f2fe;
  color: #0369a1;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.pages-info {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.price-info {
  color: #059669;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Books Statistics */
.books-stats {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem;
  color: #1f2937;
}

.stat-card p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* Responsive Table Styling for Books */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-card h4 {
    font-size: 1.25rem;
  }

  .stat-card p {
    font-size: 0.8rem;
  }

  .books-table table {
    font-size: 0.875rem;
  }

  .books-table th,
  .books-table td {
    padding: 0.5rem 0.25rem;
  }

  .edition-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .pages-info,
  .price-info {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .books-stats {
    margin-bottom: 1rem;
    padding: 0.75rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .stat-card {
    padding: 0.5rem;
  }

  .stat-card h4 {
    font-size: 1rem;
  }

  .stat-card p {
    font-size: 0.75rem;
  }

  .books-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .books-table table {
    min-width: 800px;
    font-size: 0.8rem;
  }

  .books-table th,
  .books-table td {
    padding: 0.4rem 0.2rem;
    white-space: nowrap;
  }

  .edition-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }

  .pages-info,
  .price-info {
    font-size: 0.75rem;
  }
}

/* Category Dropdown Specific Enhancements */
.select-wrapper .help-text {
  margin-top: 8px;
  padding: 6px 0;
}

/* Improved Form Validation Styling */
.form-group.error select {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group.error .help-text {
  color: #dc2626;
}

/* Success State for Category Selection */
.form-group.success select {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Category Dropdown Animation */
.select-wrapper select {
  transition: all 0.2s ease;
}

.select-wrapper select:not(:disabled):hover {
  border-color: #9ca3af;
}

/* Loading State Styling */
.select-wrapper.loading select {
  background-color: #f9fafb;
  cursor: wait;
}

/* Modal Info Section */
.modal-info {
  background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
  border: 1px solid #4f46e5;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
  font-size: 0.875rem;
  color: #4f46e5;
}

.modal-info p {
  margin: 0;
  line-height: 1.4;
}

/* Enhanced Modal Styling */
.modal-content {
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: color 0.2s ease;
}

.modal-header button:hover {
  color: #374151;
}

/* Form Enhancements */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .modal-content {
    width: 95%;
    margin: 20px auto;
  }
}

/* Login Help Section */
.login-help {
  background: linear-gradient(135deg, #f0f9ff 0%, #f3e8ff 100%);
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  font-size: 0.875rem;
}

.login-help h4 {
  margin: 0 0 12px 0;
  color: #4f46e5;
  font-size: 1rem;
  font-weight: 600;
}

.login-help ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.login-help li {
  margin-bottom: 8px;
  line-height: 1.4;
  color: #374151;
}

.login-help li:last-child {
  margin-bottom: 0;
}

.login-help strong {
  color: #1f2937;
  font-weight: 600;
}

/* Error Message Styling */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
  color: #dc2626;
  font-size: 0.875rem;
}

.error-message svg {
  flex-shrink: 0;
  color: #dc2626;
}

/* ===== OPAC (Online Public Access Catalog) Styles ===== */

/* OPAC Layout */
.opac {
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* OPAC Header */
.opac-header {
  background: #ffffff;
  color: #333333;
  padding: 1rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.opac-header .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
}

.library-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-text h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.logo-text p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.desktop-nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #666666;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
  
}

.nav-link:hover {
  color: white;
  border-radius: 10px;
  
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: #333333;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-nav {
  display: none;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 1rem;
}

.mobile-nav-link {
  color: #666666;
  text-decoration: none;
  padding: 0.5rem 0;
  font-weight: 500;
}

/* Hero Section */
.hero-section {
  background: #f8f9fa;
  color: #333333;
  padding: 3rem 0;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.hero-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  color: #666666;
}

.search-form {
  max-width: 600px;
  margin: 0 auto 2rem;
}

.search-input-group {
  display: flex;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 2;
}

.search-input {
  flex: 1;
  padding: 1rem 1rem 1rem 3rem;
  border: none;
  font-size: 1rem;
  outline: none;
  color: #374151;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-button {
  background: #333333;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.search-button:hover {
  background: #555555;
}

/* Removed conflicting quick-actions rule - using admin-home specific styles */

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: #666666;
  border: 1px solid #e5e7eb;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  background: #f8f9fa;
  border-color: #d1d5db;
}

/* Categories Section */
.categories-section {
  padding: 2rem 0;
  background: white;
}

.categories-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1f2937;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 1000px;
  margin: 0 auto;
}

.category-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-weight: 500;
  color: #374151;
}

.category-card:hover {
  background: #e9ecef;
  border-color: #d1d5db;
}

.category-card.active {
  background: #333333;
  color: white;
  border-color: #333333;
}

.category-card svg {
  flex-shrink: 0;
}

/* Catalog Section */
.catalog-section {
  padding: 2rem 0;
  background: #ffffff;
}

.catalog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.catalog-title h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: #1f2937;
}

.results-count {
  color: #6b7280;
  font-size: 0.875rem;
}

.catalog-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-toggle {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.view-btn {
  padding: 0.5rem;
  background: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.view-btn.active {
  background: #333333;
  color: white;
}

.view-btn:hover:not(.active) {
  background: #f3f4f6;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 4rem 0;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #333333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* Books Container */
.books-container {
  display: grid;
  gap: 1.5rem;
}

.books-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.books-container.list {
  grid-template-columns: 1fr;
}

/* Book Cards */
.book-card {
  background: white;
  border-radius: 6px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.book-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.books-container.grid .book-card {
  text-align: center;
}

.books-container.list .book-card {
  display: flex;
  gap: 1.5rem;
  text-align: left;
}

.book-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 100px;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin: 0 auto 1rem;
  color: #666666;
}

.books-container.list .book-cover {
  margin: 0;
  flex-shrink: 0;
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #1f2937;
  line-height: 1.4;
}

.book-author {
  color: #6b7280;
  margin: 0 0 0.5rem;
  font-style: italic;
}

.book-category {
  color: #666666;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 1rem;
}

.book-availability {
  margin: 1rem 0;
}

.availability-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.availability-badge.available {
  background: #dcfce7;
  color: #166534;
}

.availability-badge.unavailable {
  background: #fee2e2;
  color: #991b1b;
}

.book-details-preview {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #6b7280;
}

.book-details-preview p {
  margin: 0.25rem 0;
}

.view-details-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.book-card:hover .view-details-btn {
  opacity: 1;
}

.view-details-btn:hover {
  background: #4f46e5;
  color: white;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 4rem 0;
  color: #6b7280;
}

.no-results svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h4 {
  font-size: 1.25rem;
  margin: 0 0 0.5rem;
  color: #374151;
}

.no-results p {
  margin: 0;
}

/* Featured Books Section */
.featured-section {
  padding: 2rem 0;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.featured-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1f2937;
}

.featured-books {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.featured-book-card {
  background: white;
  border-radius: 6px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.featured-book-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.featured-book-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 75px;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin: 0 auto 1rem;
  color: #666666;
}

.featured-book-info h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #1f2937;
  line-height: 1.3;
}

.featured-book-info p {
  color: #6b7280;
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
}

.featured-book-rating {
  display: flex;
  justify-content: center;
  gap: 0.25rem;
}

/* About Section */
.about-section {
  padding: 2rem 0;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.about-content {
  text-align: center;
}

.about-text h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.about-text p {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #4b5563;
  margin-bottom: 2rem;
}

.library-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
}

.stat h4 {
  font-size: 2rem;
  font-weight: 700;
  color: #333333;
  margin: 0 0 0.5rem;
}

.stat p {
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.about-image {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  opacity: 0.3;
}

/* Contact Section */
.contact-section {
  padding: 2rem 0;
  background: white;
}

.contact-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #1f2937;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-card {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.contact-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-card svg {
  color: #666666;
  margin-bottom: 1rem;
}

.contact-card h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #1f2937;
}

.contact-card p {
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Footer */
.opac-footer {
  background: #333333;
  color: white;
  padding: 2rem 0 1rem;
  border-top: 1px solid #e5e7eb;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: white;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #374151;
  color: #9ca3af;
}

/* OPAC Modal Styles */
.modal .modal-content {
  max-width: 600px;
  width: 90%;
}

.advanced-search-form {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.advanced-search-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.advanced-search-form label {
  font-weight: 500;
  color: #374151;
}

.advanced-search-form input,
.advanced-search-form select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.advanced-search-form input:focus,
.advanced-search-form select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.login-options {
  text-align: center;
  padding: 1.5rem 0;
}

.login-options p {
  margin-bottom: 1.5rem;
  color: #6b7280;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.login-role-btn {
  justify-content: center;
  padding: 1rem;
  font-weight: 600;
}

.book-details-modal .modal-content {
  max-width: 700px;
}

.book-details-content {
  display: flex;
  gap: 2rem;
  margin: 1.5rem 0;
}

.book-details-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 150px;
  background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
  border-radius: 8px;
  color: #4f46e5;
  flex-shrink: 0;
}

.book-details-info {
  flex: 1;
}

.book-details-info h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: #1f2937;
}

.book-details-info .book-author {
  font-size: 1.125rem;
  color: #6b7280;
  font-style: italic;
  margin-bottom: 1.5rem;
}

.book-meta {
  display: grid;
  gap: 0.75rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.meta-item strong {
  min-width: 120px;
  color: #374151;
}

.meta-item .availability-badge {
  margin-left: 0.5rem;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive Design for OPAC */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-nav {
    display: flex;
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-button {
    border-radius: 0 0 12px 12px;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .catalog-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .books-container.grid {
    grid-template-columns: 1fr;
  }

  .books-container.list .book-card {
    flex-direction: column;
    text-align: center;
  }

  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .library-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .book-details-content {
    flex-direction: column;
    text-align: center;
  }

  .login-buttons {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .hero-content h2 {
    font-size: 1.75rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  /* Removed conflicting quick-actions responsive rule */

  .quick-action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.blue {
  background: #bee3f8;
  color: #2b6cb0;
}

.status-badge.green {
  background: #c6f6d5;
  color: #2f855a;
}

.status-badge.red {
  background: #fed7d7;
  color: #c53030;
}

.status-badge.orange {
  background: #fef5e7;
  color: #d69e2e;
}

.fine-amount {
  font-weight: 500;
  color: #2d3748;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.btn-sm {
  padding: 6px 8px;
  font-size: 0.8rem;
}

.btn-outline {
  background: transparent;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.btn-outline:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
}

.no-data-message {
  color: #718096;
}

.no-data-message svg {
  margin-bottom: 15px;
  opacity: 0.5;
}

.no-data-message h3 {
  margin: 0 0 10px 0;
  color: #4a5568;
}

.no-data-message p {
  margin: 0;
  font-size: 0.9rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f7fafc;
}

.pagination-info {
  color: #718096;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

/* Connection Status Banner */
.connection-banner {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fca5a5;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  color: #991b1b;
}

.banner-content svg {
  flex-shrink: 0;
  color: #dc2626;
}

.banner-content div {
  flex: 1;
}

.banner-content strong {
  display: block;
  margin-bottom: 5px;
  font-size: 1rem;
}

.banner-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #7f1d1d;
}

.banner-content .btn {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header svg {
  color: #667eea;
  margin-bottom: 1rem;
}

.login-header h1 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.login-header p {
  margin: 0;
  color: #4a5568;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fed7d7;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.gate-dashboard {
  min-height: 100vh;
  background: #f7fafc;
  display: flex;
  flex-direction: column;
}

.gate-header {
  background: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left svg {
  color: #667eea;
}

.header-left h1 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
}

.header-left p {
  margin: 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.scanner-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.scanner-status.connected {
  background: #c6f6d5;
  color: #22543d;
}

.scanner-status.disconnected {
  background: #fed7d7;
  color: #c53030;
}

.scanner-error {
  background: #fed7d7;
  border: 1px solid #f56565;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #c53030;
}

.scanner-error h3 {
  margin: 0 0 0.5rem 0;
}

.scanner-error p {
  margin: 0;
}

.scanning-area {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.scan-input-section {
  width: 100%;
  max-width: 600px;
}

.scan-input {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scan-input svg {
  color: #667eea;
  flex-shrink: 0;
}

.scan-input input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 500;
}

.scan-input input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.scan-result {
  width: 100%;
  max-width: 600px;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.scan-result.success {
  background: #f0fff4;
  border: 2px solid #68d391;
}

.scan-result.error {
  background: #fff5f5;
  border: 2px solid #f56565;
}

.result-icon svg {
  width: 32px;
  height: 32px;
}

.scan-result.success .result-icon svg {
  color: #38a169;
}

.scan-result.error .result-icon svg {
  color: #e53e3e;
}

.result-content {
  flex: 1;
}

.result-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
}

.scan-result.success .result-content h2 {
  color: #22543d;
}

.scan-result.error .result-content h2 {
  color: #c53030;
}

.user-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-info svg {
  color: #4a5568;
}

.user-info p {
  margin: 0.125rem 0;
  color: #4a5568;
}

.action-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.action-badge {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
}

.action-badge.entry {
  background: #c6f6d5;
  color: #22543d;
}

.action-badge.exit {
  background: #bee3f8;
  color: #2c5282;
}

.timestamp {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #718096;
  font-size: 0.8rem;
}

.recent-scans {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 0 2rem 2rem 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-scans h3 {
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.scans-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.no-scans {
  text-align: center;
  color: #718096;
  font-style: italic;
  padding: 2rem;
}

.scan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid #e2e8f0;
}

.scan-item.success {
  background: #f0fff4;
  border-left-color: #68d391;
}

.scan-item.error {
  background: #fff5f5;
  border-left-color: #f56565;
}

.scan-user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.scan-user strong {
  color: #1a202c;
}

.scan-user span {
  color: #4a5568;
  font-size: 0.9rem;
}

.scan-action {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action.entry {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.action.exit {
  background: #bee3f8;
  color: #2c5282;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.time {
  color: #718096;
  font-size: 0.8rem;
}

/* Responsive Design for Gate Entry */
@media (max-width: 768px) {
  .gate-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .scan-input {
    flex-direction: column;
    gap: 1rem;
  }

  .user-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .action-info {
    align-items: flex-start;
  }

  .scan-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Reporting System Styles */
.fine-reports, .counter-reports {
  padding: 2rem;
}

.filters-section {
  margin-bottom: 2rem;
}

.filters-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  color: #1a202c;
}

.filters-header h3 {
  margin: 0;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
  display: flex;
  align-items: end;
}

.report-type-section {
  margin-bottom: 2rem;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.type-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.type-card.active {
  border-color: #667eea;
  background: #f7fafc;
}

.type-card svg {
  color: #667eea;
  margin-bottom: 1rem;
}

.type-card h3 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.type-card p {
  margin: 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.summary-section {
  margin-bottom: 2rem;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-icon.total {
  background: #e6fffa;
  color: #319795;
}

.card-icon.paid {
  background: #f0fff4;
  color: #38a169;
}

.card-icon.unpaid {
  background: #fff5f5;
  color: #e53e3e;
}

.card-icon.count {
  background: #ebf8ff;
  color: #3182ce;
}

.card-icon.fine {
  background: #fef5e7;
  color: #d69e2e;
}

.card-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
}

.card-content p {
  margin: 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.report-table-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.report-table {
  width: 100%;
}

.report-table table {
  width: 100%;
  border-collapse: collapse;
}

.report-table th {
  background: #f7fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.9rem;
}

.report-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.9rem;
}

.report-table tr:hover {
  background: #f7fafc;
}

.report-table .amount {
  font-weight: 600;
  color: #1a202c;
}

.report-table .reason {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.report-table .status.paid {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.report-table .status.pending {
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.report-table .status.issued {
  background: #bee3f8;
  color: #2c5282;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.report-table .status.returned {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #4a5568;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #4a5568;
  text-align: center;
}

.no-data svg {
  color: #cbd5e0;
  margin-bottom: 1rem;
}

.no-data h3 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.no-data p {
  margin: 0;
  max-width: 400px;
}

/* Responsive Design for Reports */
@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .type-cards {
    grid-template-columns: 1fr;
  }

  .report-table {
    overflow-x: auto;
  }

  .report-table table {
    min-width: 800px;
  }
}

@media (max-width: 480px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .header-actions button {
    width: 100%;
  }
}

/* Gate Entry Reports Styles */
.gate-entry-reports {
  padding: 2rem;
}

.card-icon.entry {
  background: #e6fffa;
  color: #319795;
}

.card-icon.exit {
  background: #ebf8ff;
  color: #3182ce;
}

.card-icon.inside {
  background: #f0fff4;
  color: #38a169;
}

.card-icon.visitors {
  background: #fef5e7;
  color: #d69e2e;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
}

.report-table .status.in {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.report-table .status.out {
  background: #bee3f8;
  color: #2c5282;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Student Components Styles */
.available-books, .available-ebooks, .borrowing-history {
  padding: 2rem;
}

.search-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-bar {
  flex: 1;
  min-width: 300px;
  position: relative;
  display: flex;
  align-items: center;
}

.search-bar svg {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  z-index: 1;
  pointer-events: none;
}

.search-bar input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
}

.search-bar input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group select {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
}

.books-grid, .ebooks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Borrowed Books Grid for Student Dashboard */
.borrowed-books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.book-card, .ebook-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.book-card:hover, .ebook-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Clickable book cards for student dashboard */
.book-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-card.clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.book-access {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0.25rem 0;
  font-family: 'Courier New', monospace;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  max-width: 400px;
  animation: slideInRight 0.3s ease;
}

.notification.success {
  background: #10b981;
  color: white;
}

.notification.error {
  background: #ef4444;
  color: white;
}

.notification button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 1.2rem;
  line-height: 1;
  opacity: 0.8;
}

.notification button:hover {
  opacity: 1;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== INLINE BOOK DETAILS STYLES ===== */

.inline-book-details {
  grid-column: 1 / -1;
  margin: 1rem 0;
  animation: slideDown 0.3s ease;
}

.inline-book-card {
  background: white;
  border: 2px solid #667eea;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  overflow: hidden;
}

.inline-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.inline-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.inline-loading {
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.inline-error {
  padding: 2rem;
  text-align: center;
  color: #ef4444;
}

.inline-content {
  padding: 1.5rem;
}

/* Book Main Info */
.book-main-info {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.book-icon-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.book-icon-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.availability-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  text-align: center;
  min-width: 120px;
  justify-content: center;
}

.book-details-section h2 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1.2;
}

.authors-section {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.author.primary {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.875rem;
  font-weight: 600;
}

.author.secondary {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.875rem;
  font-weight: 500;
}

.book-meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 8px;
  font-size: 0.875rem;
}

.meta-item svg {
  color: #6b7280;
  flex-shrink: 0;
}

.meta-label {
  color: #6b7280;
  font-weight: 500;
}

.meta-value {
  color: #1f2937;
  font-weight: 600;
}

/* Availability Section */
.availability-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.availability-section h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.availability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.avail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 0.875rem;
}

.avail-item svg {
  color: #0284c7;
  flex-shrink: 0;
}

/* Action Section */
.action-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  color: #166534;
  font-weight: 500;
}

.text-green {
  color: #10b981;
}

.user-action {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.renewal-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-weight: 500;
}

.reservation-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #faf5ff;
  border: 1px solid #e9d5ff;
  border-radius: 8px;
  color: #7c3aed;
  font-weight: 500;
}

.btn-reserve {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Inline Book Details Responsive Design */
@media (max-width: 768px) {
  .inline-content {
    padding: 1rem;
  }

  .book-main-info {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
  }

  .book-icon-section {
    flex-direction: row;
    justify-content: center;
  }

  .book-icon-large {
    width: 60px;
    height: 60px;
  }

  .book-details-section h2 {
    font-size: 1.5rem;
  }

  .book-meta-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .availability-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .action-section {
    flex-direction: column;
  }

  .btn-reserve {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .inline-header {
    padding: 0.75rem 1rem;
  }

  .inline-header h3 {
    font-size: 1.125rem;
  }

  .book-details-section h2 {
    font-size: 1.25rem;
  }

  .authors-section {
    justify-content: center;
  }

  .meta-item {
    padding: 0.375rem;
    font-size: 0.8rem;
  }

  .avail-item {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}

.book-header, .ebook-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.book-icon, .ebook-icon {
  width: 40px;
  height: 40px;
  background: #f7fafc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
}

.availability-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.availability-badge.available {
  background: #c6f6d5;
  color: #22543d;
}

.availability-badge.unavailable {
  background: #fed7d7;
  color: #c53030;
}

.format-badge {
  background: #ebf8ff;
  color: #3182ce;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.book-title, .ebook-title {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-size: 1.1rem;
  font-weight: 600;
}

.book-details, .ebook-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.detail-item.description {
  align-items: flex-start;
}

.detail-item.description span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-footer, .ebook-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.copies-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #4a5568;
}

.download-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Borrowing History Styles */
.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: #667eea;
}

.tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e2e8f0;
}

.history-item.current {
  border-left-color: #3182ce;
}

.history-item.returned {
  border-left-color: #38a169;
}

.history-item.overdue {
  border-left-color: #e53e3e;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.book-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.book-details h3 {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
  font-size: 1.1rem;
}

.book-details p {
  margin: 0 0 0.25rem 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.access-no {
  color: #718096;
  font-size: 0.8rem;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-badge.success {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.info {
  background: #bee3f8;
  color: #2c5282;
}

.status-badge.danger {
  background: #fed7d7;
  color: #c53030;
}

.item-content {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.date-info {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.overdue-info, .fine-info, .renewal-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.overdue-info {
  background: #fed7d7;
  color: #c53030;
}

.fine-info {
  background: #fef5e7;
  color: #d69e2e;
}

.renewal-info {
  background: #ebf8ff;
  color: #3182ce;
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #667eea;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

/* Responsive Design for Student Components */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar {
    min-width: auto;
  }

  .filters {
    justify-content: space-between;
  }

  .books-grid, .ebooks-grid {
    grid-template-columns: 1fr;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .date-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }
}

/* Student Dashboard Styles */
.student-dashboard {
  height: 100vh;
  width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.student-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-info h1 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.student-info p {
  margin: 0.25rem 0 0 0;
  color: #718096;
}

.student-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.student-nav a {
  color: #4a5568;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.student-nav a:hover {
  background: #f7fafc;
}

.student-nav button {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.student-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.student-home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.user-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.user-details p {
  margin: 0;
  color: #4a5568;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.borrowed-books-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.borrowed-books-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #718096;
}

.empty-state p {
  margin: 1rem 0;
  font-size: 1.1rem;
}

.borrowed-books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.book-card {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.book-card.overdue {
  border-left-color: #e53e3e;
  background: #fed7d7;
}

.book-info h3 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
}

.book-info p {
  margin: 0;
  color: #718096;
}

.book-dates {
  margin-top: 1rem;
}

.date-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #4a5568;
}

.overdue-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #c53030;
  font-weight: 500;
  font-size: 0.9rem;
}

.days-remaining {
  color: #38a169;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Admin Dashboard Quick Actions */
.admin-home .quick-actions {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.admin-home .quick-actions h2 {
  margin: 0 0 1.5rem 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-home .quick-actions h2::before {
  content: "⚡";
  font-size: 1.25rem;
}

.admin-home .action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
}

.admin-home .action-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem;
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160px;
}

.admin-home .action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.admin-home .action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.admin-home .action-card:hover {
  background: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
  border-color: #cbd5e0;
}

.admin-home .action-card:hover .action-icon {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.admin-home .action-card h3 {
  margin: 0 0 0.75rem 0;
  color: #1a202c;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.3;
}

.admin-home .action-card p {
  margin: 0 0 1.25rem 0;
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.5;
  flex-grow: 1;
}

.admin-home .action-button {
  margin-top: auto;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
  align-self: flex-start;
}

/* Admin Dashboard Recent Activity */
.admin-home .recent-activity {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.admin-home .recent-activity h2 {
  margin: 0 0 1.5rem 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-home .recent-activity h2::before {
  content: "📋";
  font-size: 1.25rem;
}

.admin-home .activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-home .activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #e2e8f0;
  transition: all 0.2s ease;
}

.admin-home .activity-item:hover {
  background: #f1f5f9;
  border-left-color: #667eea;
  transform: translateX(4px);
}

.admin-home .activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 8px;
  flex-shrink: 0;
}

.admin-home .activity-content {
  flex: 1;
  min-width: 0;
}

.admin-home .activity-content p {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.95rem;
  line-height: 1.4;
}

.admin-home .activity-content small {
  color: #718096;
  font-size: 0.8rem;
}

/* Admin Dashboard Responsive Design */
@media (max-width: 1200px) {
  .admin-home .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }

  .admin-home .quick-actions,
  .admin-home .recent-activity {
    padding: 1.5rem;
  }

  .admin-home .action-card {
    padding: 1.25rem;
    min-height: 140px;
  }
}

@media (max-width: 768px) {
  .admin-home .quick-actions,
  .admin-home .recent-activity {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .admin-home .quick-actions h2,
  .admin-home .recent-activity h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .admin-home .action-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-home .action-card {
    padding: 1rem;
    min-height: 120px;
  }

  .admin-home .action-card h3 {
    font-size: 1rem;
  }

  .admin-home .action-card p {
    font-size: 0.85rem;
    margin-bottom: 1rem;
  }

  .admin-home .action-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }

  .admin-home .activity-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .admin-home .activity-icon {
    width: 36px;
    height: 36px;
  }

  .admin-home .activity-content p {
    font-size: 0.9rem;
  }

  .admin-home .activity-content small {
    font-size: 0.75rem;
  }
}

/* ===== LIBRARIAN DASHBOARD STYLES ===== */

/* Librarian Home Container */
.librarian-home {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.librarian-home .page-header {
  margin-bottom: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.librarian-home .page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.librarian-home .page-header p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Librarian Stats Grid */
.librarian-home .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 0;
}

.librarian-home .stat-card {
  background: white;
  padding: 1.75rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 1.25rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.librarian-home .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color, #667eea);
  transition: height 0.3s ease;
}

.librarian-home .stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e0;
}

.librarian-home .stat-card:hover::before {
  height: 6px;
}

/* Color variants for stat cards */
.librarian-home .stat-card.blue {
  --card-color: #3b82f6;
}

.librarian-home .stat-card.purple {
  --card-color: #8b5cf6;
}

.librarian-home .stat-card.green {
  --card-color: #10b981;
}

.librarian-home .stat-card.teal {
  --card-color: #14b8a6;
}

.librarian-home .stat-card.yellow {
  --card-color: #f59e0b;
}

.librarian-home .stat-card.red {
  --card-color: #ef4444;
}

.librarian-home .stat-card.orange {
  --card-color: #f97316;
}

.librarian-home .stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-color, #667eea);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.librarian-home .stat-card:hover .stat-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.librarian-home .stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.librarian-home .stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
}

.librarian-home .stat-title {
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  font-size: 1rem;
  line-height: 1.3;
}

.librarian-home .stat-description {
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0;
}

/* Librarian Quick Actions */
.librarian-home .quick-actions {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.librarian-home .quick-actions h2 {
  margin: 0 0 2rem 0;
  color: #1a202c;
  font-size: 1.75rem;
  font-weight: 700;
  text-align: center;
  position: relative;
  padding-bottom: 1rem;
}

.librarian-home .quick-actions h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.librarian-home .action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
}

.librarian-home .action-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 2rem;
  border-radius: 16px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 200px;
  justify-content: center;
  cursor: pointer;
}

.librarian-home .action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.librarian-home .action-card:hover::before {
  transform: scaleX(1);
}

.librarian-home .action-card:hover {
  background: white;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-6px);
  border-color: #cbd5e0;
}

.librarian-home .action-card svg {
  color: #667eea;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.librarian-home .action-card:hover svg {
  color: #764ba2;
  transform: scale(1.1);
}

.librarian-home .action-card h3 {
  margin: 0 0 0.75rem 0;
  color: #1a202c;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
}

.librarian-home .action-card p {
  margin: 0;
  color: #4a5568;
  font-size: 0.95rem;
  line-height: 1.5;
}

@media (max-width: 480px) {
  .admin-home .quick-actions,
  .admin-home .recent-activity {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .admin-home .action-card {
    padding: 0.75rem;
    min-height: 100px;
  }

  .admin-home .action-button {
    width: 100%;
    text-align: center;
  }

  /* Librarian Dashboard Responsive Design */
  .librarian-home {
    padding: 1rem;
  }

  .librarian-home .page-header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .librarian-home .page-header h1 {
    font-size: 2rem;
  }

  .librarian-home .page-header p {
    font-size: 1rem;
  }

  .librarian-home .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
  }

  .librarian-home .stat-card {
    padding: 1.5rem;
  }

  .librarian-home .stat-icon {
    width: 56px;
    height: 56px;
  }

  .librarian-home .stat-content h3 {
    font-size: 1.75rem;
  }

  .librarian-home .quick-actions {
    padding: 2rem;
  }

  .librarian-home .quick-actions h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .librarian-home .action-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }

  .librarian-home .action-card {
    padding: 1.5rem;
    min-height: 180px;
  }

  .librarian-home .action-card h3 {
    font-size: 1.125rem;
  }

  .librarian-home .action-card p {
    font-size: 0.9rem;
  }
}

/* Mobile Responsive Design for Librarian Dashboard */
@media (max-width: 768px) {
  .librarian-home {
    padding: 0.75rem;
  }

  .librarian-home .page-header {
    padding: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .librarian-home .page-header h1 {
    font-size: 1.75rem;
  }

  .librarian-home .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .librarian-home .stat-card {
    padding: 1.25rem;
  }

  .librarian-home .stat-icon {
    width: 48px;
    height: 48px;
  }

  .librarian-home .stat-content h3 {
    font-size: 1.5rem;
  }

  .librarian-home .quick-actions {
    padding: 1.5rem;
  }

  .librarian-home .quick-actions h2 {
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .librarian-home .action-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .librarian-home .action-card {
    padding: 1.25rem;
    min-height: 160px;
  }

  .librarian-home .action-card h3 {
    font-size: 1rem;
  }

  .librarian-home .action-card p {
    font-size: 0.875rem;
  }
}

/* Extra Small Mobile Responsive Design */
@media (max-width: 480px) {
  .librarian-home .page-header h1 {
    font-size: 1.5rem;
  }

  .librarian-home .page-header p {
    font-size: 0.9rem;
  }

  .librarian-home .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .librarian-home .stat-icon {
    width: 44px;
    height: 44px;
  }

  .librarian-home .stat-content h3 {
    font-size: 1.25rem;
  }

  .librarian-home .quick-actions {
    padding: 1rem;
  }

  .librarian-home .action-card {
    padding: 1rem;
    min-height: 140px;
  }

  .librarian-home .action-card svg {
    width: 28px;
    height: 28px;
  }
}

/* ===== RESERVATION MANAGEMENT STYLES ===== */

/* Main Container */
.reservation-management {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Page Header */
.reservation-management .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: #667eea;
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.reservation-management .header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.25rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reservation-management .header-content p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 300;
}

.reservation-management .page-header .btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.reservation-management .page-header .btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Filters Section */
.reservation-management .filters-section {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.reservation-management .search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.reservation-management .search-box svg {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 1;
}

.reservation-management .search-box input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reservation-management .search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.reservation-management .filter-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 0.875rem 1.25rem;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.reservation-management .filter-group:hover {
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.reservation-management .filter-group svg {
  color: #6b7280;
}

.reservation-management .filter-group select {
  border: none;
  background: none;
  font-size: 1rem;
  color: #374151;
  cursor: pointer;
  outline: none;
  font-weight: 500;
}

/* Table Container */
.reservation-management .table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-bottom: 2rem;
}

/* Data Table */
.reservation-management .data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.reservation-management .data-table thead {
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
}

.reservation-management .data-table th {
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.reservation-management .data-table th:first-child {
  padding-left: 1.5rem;
}

.reservation-management .data-table th:last-child {
  padding-right: 1.5rem;
}

.reservation-management .data-table tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.reservation-management .data-table tbody tr:nth-child(even) {
  background: #fafbfc;
}

.reservation-management .data-table tbody tr:hover {
  background: #f0f9ff;
  transform: scale(1.001);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reservation-management .data-table td {
  padding: 1.25rem 1rem;
  vertical-align: middle;
  border-bottom: 1px solid #f3f4f6;
}

.reservation-management .data-table td:first-child {
  padding-left: 1.5rem;
}

.reservation-management .data-table td:last-child {
  padding-right: 1.5rem;
}

/* User Information Styling */
.reservation-management .user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.reservation-management .user-avatar {
  width: 40px;
  height: 40px;
  background: #667eea;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.reservation-management .user-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

.reservation-management .user-id {
  font-size: 0.8rem;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

/* Book Information Styling */
.reservation-management .book-info {
  max-width: 300px;
}

.reservation-management .book-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.reservation-management .book-details {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.3;
}

/* Queue Position Styling */
.reservation-management .queue-position {
  background: #f59e0b;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.2);
  display: inline-block;
}

/* Status Badge Styling */
.reservation-management .data-table .bg-blue-100 {
  background: #dbeafe !important;
  color: #1e40af !important;
  border: 1px solid #93c5fd;
}

.reservation-management .data-table .bg-green-100 {
  background: #dcfce7 !important;
  color: #166534 !important;
  border: 1px solid #86efac;
}

.reservation-management .data-table .bg-red-100 {
  background: #fee2e2 !important;
  color: #991b1b !important;
  border: 1px solid #fca5a5;
}

.reservation-management .data-table .bg-gray-100 {
  background: #f3f4f6 !important;
  color: #374151 !important;
  border: 1px solid #d1d5db;
}

/* Pickup Deadline Styling */
.reservation-management .pickup-deadline {
  background: #fef3c7;
  color: #92400e;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #fcd34d;
}

.reservation-management .text-muted {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.85rem;
}

/* Action Buttons */
.reservation-management .action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.reservation-management .btn-sm {
  padding: 0.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reservation-management .btn-sm:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.reservation-management .btn-sm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.reservation-management .btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.reservation-management .btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.reservation-management .btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.reservation-management .btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* Loading State */
.reservation-management .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.reservation-management .loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.reservation-management .loading-state p {
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
}

/* Empty State */
.reservation-management .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  text-align: center;
}

.reservation-management .empty-state svg {
  color: #d1d5db;
  margin-bottom: 1.5rem;
}

.reservation-management .empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.reservation-management .empty-state p {
  font-size: 1rem;
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Notification Styles */
.reservation-management .notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  max-width: 400px;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  animation: slideInRight 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reservation-management .notification.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.reservation-management .notification.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.reservation-management .notification button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.reservation-management .notification button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Pagination Styles */
.reservation-management .pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem 0;
}

.reservation-management .pagination .btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reservation-management .pagination .btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.reservation-management .pagination .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.reservation-management .page-info {
  font-weight: 600;
  color: #374151;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* Spinning Animation */
.reservation-management .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design for Reservation Management */

/* Tablet Responsive Design */
@media (max-width: 1024px) {
  .reservation-management {
    padding: 1.5rem;
  }

  .reservation-management .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1.5rem;
  }

  .reservation-management .header-content h1 {
    font-size: 2rem;
  }

  .reservation-management .filters-section {
    flex-direction: column;
    gap: 1rem;
  }

  .reservation-management .search-box {
    min-width: auto;
    max-width: none;
  }

  .reservation-management .data-table {
    font-size: 0.875rem;
  }

  .reservation-management .data-table th,
  .reservation-management .data-table td {
    padding: 1rem 0.75rem;
  }

  .reservation-management .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .reservation-management .user-avatar {
    width: 32px;
    height: 32px;
  }

  .reservation-management .book-info {
    max-width: 250px;
  }

  .reservation-management .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .reservation-management .btn-sm {
    padding: 0.375rem;
  }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .reservation-management {
    padding: 1rem;
  }

  .reservation-management .page-header {
    padding: 1.25rem;
  }

  .reservation-management .header-content h1 {
    font-size: 1.75rem;
  }

  .reservation-management .header-content p {
    font-size: 1rem;
  }

  .reservation-management .search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    font-size: 0.9rem;
  }

  .reservation-management .filter-group {
    padding: 0.75rem 1rem;
  }

  .reservation-management .table-container {
    overflow-x: auto;
    border-radius: 12px;
  }

  .reservation-management .data-table {
    min-width: 800px;
    font-size: 0.8rem;
  }

  .reservation-management .data-table th,
  .reservation-management .data-table td {
    padding: 0.875rem 0.5rem;
  }

  .reservation-management .data-table th:first-child,
  .reservation-management .data-table td:first-child {
    padding-left: 1rem;
  }

  .reservation-management .data-table th:last-child,
  .reservation-management .data-table td:last-child {
    padding-right: 1rem;
  }

  .reservation-management .user-name,
  .reservation-management .book-title {
    font-size: 0.875rem;
  }

  .reservation-management .user-id,
  .reservation-management .book-details {
    font-size: 0.75rem;
  }

  .reservation-management .queue-position {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .reservation-management .notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }

  .reservation-management .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .reservation-management .pagination .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .reservation-management {
    padding: 0.75rem;
  }

  .reservation-management .page-header {
    padding: 1rem;
  }

  .reservation-management .header-content h1 {
    font-size: 1.5rem;
  }

  .reservation-management .header-content p {
    font-size: 0.9rem;
  }

  .reservation-management .search-box input {
    padding: 0.625rem 0.875rem 0.625rem 2.25rem;
    font-size: 0.875rem;
  }

  .reservation-management .filter-group {
    padding: 0.625rem 0.875rem;
  }

  .reservation-management .data-table {
    min-width: 700px;
    font-size: 0.75rem;
  }

  .reservation-management .data-table th,
  .reservation-management .data-table td {
    padding: 0.75rem 0.375rem;
  }

  .reservation-management .user-avatar {
    width: 28px;
    height: 28px;
  }

  .reservation-management .btn-sm {
    padding: 0.25rem;
  }

  .reservation-management .btn-sm svg {
    width: 12px;
    height: 12px;
  }

  .reservation-management .empty-state,
  .reservation-management .loading-state {
    padding: 3rem 1rem;
  }

  .reservation-management .empty-state h3 {
    font-size: 1.25rem;
  }

  .reservation-management .empty-state p {
    font-size: 0.9rem;
  }
}

/* Print Styles */
@media print {
  .reservation-management .page-header .btn,
  .reservation-management .filters-section,
  .reservation-management .action-buttons,
  .reservation-management .pagination,
  .reservation-management .notification {
    display: none !important;
  }

  .reservation-management {
    padding: 0;
    background: white;
  }

  .reservation-management .page-header {
    background: none;
    color: black;
    box-shadow: none;
    border-bottom: 2px solid #000;
    margin-bottom: 1rem;
  }

  .reservation-management .table-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .reservation-management .data-table th,
  .reservation-management .data-table td {
    border: 1px solid #000;
  }
}

/* Enhanced Button Styles for Reservation Management */
.reservation-management .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.reservation-management .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.reservation-management .btn:hover::before {
  left: 100%;
}

.reservation-management .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.reservation-management .btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reservation-management .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reservation-management .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.reservation-management .btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.reservation-management .btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

/* Status Badge Enhancements */
.reservation-management .data-table span[class*="bg-"] {
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.reservation-management .data-table span[class*="bg-"]:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Date Formatting */
.reservation-management .date-display {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  color: #4b5563;
  background: #f9fafb;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

/* Utility Classes */
.reservation-management .text-center {
  text-align: center;
}

.reservation-management .text-left {
  text-align: left;
}

.reservation-management .text-right {
  text-align: right;
}

.reservation-management .font-mono {
  font-family: 'Monaco', 'Menlo', monospace;
}

.reservation-management .font-semibold {
  font-weight: 600;
}

.reservation-management .font-bold {
  font-weight: 700;
}

.reservation-management .text-sm {
  font-size: 0.875rem;
}

.reservation-management .text-xs {
  font-size: 0.75rem;
}

.reservation-management .text-lg {
  font-size: 1.125rem;
}

.reservation-management .mb-2 {
  margin-bottom: 0.5rem;
}

.reservation-management .mb-4 {
  margin-bottom: 1rem;
}

.reservation-management .mt-2 {
  margin-top: 0.5rem;
}

.reservation-management .mt-4 {
  margin-top: 1rem;
}

/* Focus States for Accessibility */
.reservation-management .btn:focus,
.reservation-management input:focus,
.reservation-management select:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.reservation-management .data-table tbody tr:focus-within {
  background: #f0f9ff;
  outline: 2px solid #667eea;
  outline-offset: -2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .reservation-management .data-table th {
    background: #000;
    color: #fff;
  }

  .reservation-management .data-table tbody tr:nth-child(even) {
    background: #f0f0f0;
  }

  .reservation-management .data-table tbody tr:hover {
    background: #e0e0e0;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .reservation-management * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== RESERVATION MODAL STYLES ===== */

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* Modal Content */
.reservation-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.reservation-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #667eea;
  color: white;
  border-radius: 16px 16px 0 0;
}

.reservation-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.reservation-modal .modal-close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.reservation-modal .modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Modal Body */
.reservation-modal .modal-body {
  padding: 2rem;
}

/* Book Info Section */
.reservation-modal .book-info-section {
  display: flex;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border: 1px solid #e5e7eb;
}

.reservation-modal .book-icon {
  width: 48px;
  height: 48px;
  background: #667eea;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.reservation-modal .book-details h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.reservation-modal .book-details p {
  margin: 0.25rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.reservation-modal .book-details .access-no {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #e5e7eb;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  font-size: 0.8rem;
}

.reservation-modal .book-details .availability {
  color: #059669;
  font-weight: 500;
}

/* Form Styling */
.reservation-modal .form-group {
  margin-bottom: 1.5rem;
}

.reservation-modal .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.reservation-modal .form-group input,
.reservation-modal .form-group textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.reservation-modal .form-group input:focus,
.reservation-modal .form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.reservation-modal .form-group input.error,
.reservation-modal .form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.reservation-modal .error-text {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.reservation-modal .help-text {
  color: #6b7280;
  font-size: 0.8rem;
  margin-top: 0.375rem;
  line-height: 1.4;
}

/* Info Box */
.reservation-modal .info-box {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  gap: 0.75rem;
}

.reservation-modal .info-box svg {
  color: #0284c7;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.reservation-modal .info-box p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #0c4a6e;
}

.reservation-modal .info-box ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #075985;
}

.reservation-modal .info-box li {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Availability Information */
.reservation-modal .availability-info {
  background: #fef3c7;
  border: 1px solid #fcd34d;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.75rem;
  display: flex;
  gap: 0.75rem;
}

.reservation-modal .availability-info svg {
  color: #d97706;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.reservation-modal .availability-info p {
  margin: 0 0 0.5rem 0;
  color: #92400e;
  font-size: 0.9rem;
  line-height: 1.4;
}

.reservation-modal .availability-info p:last-child {
  margin-bottom: 0;
}

/* Modal Actions */
.reservation-modal .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.reservation-modal .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.reservation-modal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reservation-modal .btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.reservation-modal .btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.reservation-modal .btn-primary {
  background: #667eea;
  color: white;
}

.reservation-modal .btn-primary:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.reservation-modal .spinning {
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 640px) {
  .reservation-modal {
    width: 95%;
    margin: 1rem;
  }

  .reservation-modal .modal-header {
    padding: 1.25rem 1.5rem;
  }

  .reservation-modal .modal-header h2 {
    font-size: 1.25rem;
  }

  .reservation-modal .modal-body {
    padding: 1.5rem;
  }

  .reservation-modal .book-info-section {
    flex-direction: column;
    text-align: center;
  }

  .reservation-modal .modal-actions {
    flex-direction: column;
  }

  .reservation-modal .btn {
    justify-content: center;
  }
}

/* ===== MY RESERVATIONS STYLES ===== */

.my-reservations {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.my-reservations .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: #667eea;
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.my-reservations .header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.25rem;
  font-weight: 700;
}

.my-reservations .header-content p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 300;
}

.my-reservations .reservations-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* Reservations Grid */
.my-reservations .reservations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

/* Reservation Card */
.my-reservations .reservation-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.my-reservations .reservation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.my-reservations .reservation-card.active {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.my-reservations .reservation-card.fulfilled {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.my-reservations .reservation-card.cancelled {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.my-reservations .reservation-card.expired {
  border-color: #6b7280;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

/* Reservation Header */
.my-reservations .reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.my-reservations .book-info {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.my-reservations .book-icon {
  width: 48px;
  height: 48px;
  background: #667eea;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.my-reservations .book-details h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.my-reservations .book-details p {
  margin: 0.125rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.my-reservations .book-details .access-no {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #e5e7eb;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  font-size: 0.8rem;
}

/* Status Badges */
.my-reservations .status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.8rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.my-reservations .status-active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #86efac;
}

.my-reservations .status-fulfilled {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.my-reservations .status-cancelled {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.my-reservations .status-expired {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

/* Reservation Details */
.my-reservations .reservation-details {
  margin-bottom: 1rem;
}

.my-reservations .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.my-reservations .detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.my-reservations .detail-row .label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.9rem;
}

.my-reservations .queue-position {
  background: #f59e0b;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.8rem;
}

.my-reservations .pickup-urgent {
  color: #dc2626;
  font-weight: 600;
}

.my-reservations .notes {
  font-style: italic;
  color: #4b5563;
  max-width: 200px;
  word-wrap: break-word;
}

/* Urgency Notice */
.my-reservations .urgency-notice {
  background: #fef3c7;
  border: 1px solid #fcd34d;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Reservation Actions */
.my-reservations .reservation-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.my-reservations .btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-weight: 500;
}

.my-reservations .btn-danger {
  background: #ef4444;
  color: white;
}

.my-reservations .btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.my-reservations .btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== ENHANCED QUICK ACTIONS STYLES ===== */

.quick-actions {
  margin-bottom: 2rem;
}

.quick-actions h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

.action-card.browse-books .action-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-card.browse-ebooks .action-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-card.my-reservations .action-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-card.borrowing-history .action-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-content {
  flex: 1;
}

.action-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  transition: color 0.3s ease;
}

.action-card:hover .action-content h3 {
  color: #667eea;
}

.action-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.action-arrow {
  font-size: 1.5rem;
  color: #d1d5db;
  transition: all 0.3s ease;
  font-weight: bold;
}

.action-card:hover .action-arrow {
  color: #667eea;
  transform: translateX(4px);
}

/* Responsive Design for Quick Actions */
@media (max-width: 768px) {
  .action-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-card {
    padding: 1.25rem;
  }

  .action-icon {
    width: 48px;
    height: 48px;
  }

  .action-content h3 {
    font-size: 1rem;
  }

  .action-content p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .action-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
  }

  .action-arrow {
    display: none;
  }
}

/* Continue with admin styles */
.admin-home .activity-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
  }

  .admin-home .activity-icon {
    width: 32px;
    height: 32px;
  }
/* Admin Dashboard Overall Layout */
.admin-home {
  padding: 2rem;
  background: #f7fafc;
  min-height: calc(100vh - 80px);
}

.admin-home .page-header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: center;
}

.admin-home .page-header h1 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-home .page-header p {
  margin: 0;
  color: #4a5568;
  font-size: 1.125rem;
  font-weight: 500;
}

/* Duplicate section removed - styles already defined above */

.admin-home .activity-item:hover {
  background: #f1f5f9;
  border-left-color: #667eea;
  transform: translateX(4px);
}

.admin-home .activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 8px;
  flex-shrink: 0;
}

.admin-home .activity-content {
  flex: 1;
  min-width: 0;
}

.admin-home .activity-content p {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.95rem;
  line-height: 1.4;
}

.admin-home .activity-content small {
  color: #718096;
  font-size: 0.8rem;
}

/* Content Placeholder */
.content-placeholder {
  background: white;
  padding: 3rem;
  border-radius: 8px;
  text-align: center;
  color: #718096;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-placeholder p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .student-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .student-nav {
    width: 100%;
    justify-content: space-between;
  }
}

/* Question Bank Styles */
.question-banks-section {
  padding: 80px 0;
  background: #f8fafc;
  color: #1a202c;
  border-top: 1px solid #e2e8f0;
}

.question-banks-section .section-header {
  text-align: center;
  margin-bottom: 60px;
}

.question-banks-section .section-header h3 {
  font-size: 2.5rem;
  margin-bottom: 16px;
  font-weight: 700;
  color: #2d3748;
}

.question-banks-section .section-header p {
  font-size: 1.2rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
}

.qb-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.qb-feature-card {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.qb-feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.qb-feature-icon {
  background: #667eea;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
}

.qb-feature-card h4 {
  font-size: 1.5rem;
  margin-bottom: 16px;
  font-weight: 600;
  color: #2d3748;
}

.qb-feature-card p {
  color: #4a5568;
  line-height: 1.6;
}

.qb-cta {
  text-align: center;
}

.qb-cta p {
  font-size: 1.2rem;
  margin-bottom: 24px;
  color: #4a5568;
}

.qb-cta .btn {
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.qb-cta .btn-primary {
  background: #667eea;
  color: white;
}

.qb-cta .btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.qb-cta .btn-secondary {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.qb-cta .btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Student Question Bank Search Styles */
.action-card.question-banks {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-card.question-banks:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
}

/* OPAC Button Flex Layout */
.qb-cta .flex {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

@media (max-width: 640px) {
  .qb-cta .flex {
    flex-direction: column;
    gap: 0.75rem;
  }

  .qb-cta .btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
}

/* OPAC Search Status and Controls */
.search-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.search-term {
  color: #4a5568;
  font-size: 0.9rem;
}

.clear-search-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-search-btn:hover {
  background: #dc2626;
}

.show-all-section {
  margin-top: 0.5rem;
}

.show-all-btn {
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.show-all-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.catalog-header {
  margin-bottom: 2rem;
}

.catalog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.results-count {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .catalog-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .search-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .clear-search-btn {
    align-self: flex-end;
  }
}

/* Student Dashboard Quick Actions - Modern Design */
.quick-actions-section {
  margin: 2rem 0;
}

.quick-actions-section .section-header {
  margin-bottom: 1.5rem;
}

.quick-actions-section .section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.quick-actions-section .section-header p {
  color: #4a5568;
  font-size: 0.9rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.quick-action-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: block;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e0;
}

.quick-action-card .card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.quick-action-card .action-icon-wrapper {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.quick-action-card .action-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.quick-action-card .action-details p {
  color: #4a5568;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0;
}

.quick-action-card .card-footer {
  border-top: 1px solid #f1f5f9;
  padding-top: 1rem;
  margin-top: 1rem;
}

.quick-action-card .action-link {
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.quick-action-card:hover .action-link {
  color: #5a6fd8;
}

/* Specific color themes for different actions */
.quick-action-card.browse-books:hover .action-icon-wrapper {
  background: #ebf8ff;
  border-color: #90cdf4;
  color: #2b6cb0;
}

.quick-action-card.browse-ebooks:hover .action-icon-wrapper {
  background: #f0fff4;
  border-color: #9ae6b4;
  color: #2f855a;
}

.quick-action-card.question-banks:hover .action-icon-wrapper {
  background: #fef5e7;
  border-color: #f6ad55;
  color: #c05621;
}

.quick-action-card.my-reservations:hover .action-icon-wrapper {
  background: #fdf2f8;
  border-color: #f687b3;
  color: #b83280;
}

.quick-action-card.borrowing-history:hover .action-icon-wrapper {
  background: #f7fafc;
  border-color: #a0aec0;
  color: #4a5568;
}

@media (max-width: 768px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-action-card {
    padding: 1.25rem;
  }

  .quick-action-card .card-header {
    gap: 0.75rem;
  }

  .quick-action-card .action-icon-wrapper {
    padding: 0.625rem;
  }
}
