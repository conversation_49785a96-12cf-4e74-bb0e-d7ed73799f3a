#!/usr/bin/env python3
"""
Test script to verify OPAC API endpoints are working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Book, Category
import requests
import json

def test_database_connection():
    """Test if we can connect to the database and query books"""
    print("🔍 Testing Database Connection...")
    
    with app.app_context():
        try:
            # Test basic database connection
            book_count = Book.query.count()
            category_count = Category.query.count()
            
            print(f"✓ Database connected successfully")
            print(f"✓ Books in database: {book_count}")
            print(f"✓ Categories in database: {category_count}")
            
            # Show first few books
            if book_count > 0:
                print("\n📚 Sample books in database:")
                books = Book.query.limit(5).all()
                for book in books:
                    print(f"  - {book.title} by {book.author} (Available: {book.available_copies})")
            else:
                print("⚠️  No books found in database")
                
            return book_count > 0
            
        except Exception as e:
            print(f"✗ Database connection failed: {str(e)}")
            return False

def test_api_endpoints():
    """Test the public API endpoints"""
    print("\n🌐 Testing API Endpoints...")
    
    base_url = "http://localhost:5000"
    
    # Test books search endpoint
    try:
        print("Testing /api/books/search...")
        response = requests.get(f"{base_url}/api/books/search?per_page=10", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            books = data.get('books', [])
            print(f"✓ Books API working - returned {len(books)} books")
            
            if books:
                print("📖 Sample API response:")
                for book in books[:3]:
                    print(f"  - {book.get('title')} by {book.get('author')}")
            else:
                print("⚠️  API returned no books")
                
        else:
            print(f"✗ Books API failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to Flask server. Is it running on localhost:5000?")
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")
    
    # Test categories endpoint
    try:
        print("\nTesting /api/categories/public...")
        response = requests.get(f"{base_url}/api/categories/public", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            print(f"✓ Categories API working - returned {len(categories)} categories")
            
            if categories:
                print("📂 Available categories:")
                for cat in categories:
                    print(f"  - {cat.get('name')}")
            else:
                print("⚠️  API returned no categories")
                
        else:
            print(f"✗ Categories API failed with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to Flask server for categories endpoint")
    except Exception as e:
        print(f"✗ Categories API test failed: {str(e)}")

def create_test_data_if_needed():
    """Create test data if database is empty"""
    print("\n📝 Checking if test data is needed...")
    
    with app.app_context():
        try:
            book_count = Book.query.count()
            
            if book_count == 0:
                print("Creating test data...")
                
                # Create a test category
                test_category = Category(
                    name="Test Fiction",
                    description="Test category for fiction books",
                    created_by=1
                )
                db.session.add(test_category)
                
                # Create test books
                test_books = [
                    {
                        "access_no": "TEST001",
                        "title": "Test Book 1",
                        "author": "Test Author 1",
                        "publisher": "Test Publisher",
                        "category": "Test Fiction",
                        "department": "Literature",
                        "location": "A-1-001",
                        "number_of_copies": 3,
                        "available_copies": 3,
                        "isbn": "9780000000001"
                    },
                    {
                        "access_no": "TEST002",
                        "title": "Test Book 2",
                        "author": "Test Author 2",
                        "publisher": "Test Publisher",
                        "category": "Test Fiction",
                        "department": "Literature",
                        "location": "A-1-002",
                        "number_of_copies": 2,
                        "available_copies": 2,
                        "isbn": "9780000000002"
                    }
                ]
                
                for book_data in test_books:
                    book = Book(**book_data)
                    db.session.add(book)
                
                db.session.commit()
                print("✓ Test data created successfully")
                
                return True
            else:
                print(f"✓ Database already has {book_count} books")
                return False
                
        except Exception as e:
            print(f"✗ Failed to create test data: {str(e)}")
            db.session.rollback()
            return False

def main():
    """Run all tests"""
    print("🧪 OPAC API TESTING SUITE")
    print("=" * 50)
    
    # Test database connection
    db_ok = test_database_connection()
    
    # Create test data if needed
    if not db_ok:
        create_test_data_if_needed()
        test_database_connection()  # Test again after creating data
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("🏁 TESTING COMPLETE")
    print("\nNext steps:")
    print("1. If Flask server is not running: python app.py")
    print("2. If no books shown: python create_sample_data.py")
    print("3. Check React console for any frontend errors")
    print("4. Visit http://localhost:3000 to test OPAC")

if __name__ == '__main__':
    main()
