@tailwind base;
@tailwind components;
@tailwind utilities;p
:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;/*  */
  }
}

/* Gate Entry Dashboard Enhancements */
.scan-result {
  position: relative;
}

.close-result-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-result-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.scan-result.success .close-result-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.scan-result.error .close-result-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.barcode-info {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-family: monospace;
}

.error-details {
  margin-top: 10px;
}

.error-message {
  color: #d32f2f;
  font-weight: 500;
  margin: 0;
}

.user-details .email {
  font-size: 0.9em;
  color: #666;
  margin-top: 2px;
}

/* Gate Entry Logs Table Styles */
.gate-logs-section {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e0e0e0;
}

.logs-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.logs-table-container {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.logs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.logs-table thead {
  background-color: #f8f9fa;
}

.logs-table th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
}

.logs-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.logs-table tbody tr:hover {
  background-color: #f8f9fa;
}

.log-row.in {
  border-left: 4px solid #28a745;
}

.log-row.out {
  border-left: 4px solid #dc3545;
}

.user-id {
  font-family: monospace;
  font-weight: 600;
  color: #495057;
}

.name {
  font-weight: 500;
  color: #212529;
}

.in-time, .out-time {
  font-family: monospace;
  color: #6c757d;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.in {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.out {
  background-color: #f8d7da;
  color: #721c24;
}

.date {
  color: #6c757d;
  font-size: 12px;
}

.no-data {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Responsive table */
@media (max-width: 768px) {
  .logs-table {
    font-size: 12px;
  }

  .logs-table th,
  .logs-table td {
    padding: 8px 4px;
  }

  .logs-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
