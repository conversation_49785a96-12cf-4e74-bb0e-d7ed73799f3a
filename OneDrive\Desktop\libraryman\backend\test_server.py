#!/usr/bin/env python3

print("Starting server test...")

try:
    print("Importing Flask...")
    from flask import Flask
    
    print("Importing app...")
    from app import app
    
    print("Testing app context...")
    with app.app_context():
        print("App context works!")
        
    print("Starting server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
