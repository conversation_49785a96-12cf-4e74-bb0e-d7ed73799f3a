#!/usr/bin/env python3
"""
Test script to verify the fixes implemented:
1. Category creation functionality
2. User authentication with User ID
3. Password generation with userid+userid format
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Category, User
import requests
import json

def test_category_creation():
    """Test category creation functionality"""
    print("\n" + "="*50)
    print("TESTING CATEGORY CREATION")
    print("="*50)
    
    with app.app_context():
        try:
            # Create the tables
            db.create_all()
            print("✓ Database tables created/verified")
            
            # Test creating a category
            test_category = Category(
                name="Test Fiction",
                description="Test category for fiction books",
                created_by=1
            )
            
            db.session.add(test_category)
            db.session.commit()
            print("✓ Test category created successfully")
            
            # Verify category exists
            category = Category.query.filter_by(name="Test Fiction").first()
            if category:
                print(f"✓ Category verified: {category.name} (ID: {category.id})")
            else:
                print("✗ Category not found after creation")
                
            # Clean up
            db.session.delete(category)
            db.session.commit()
            print("✓ Test category cleaned up")
            
        except Exception as e:
            print(f"✗ Category creation test failed: {str(e)}")
            db.session.rollback()

def test_password_generation():
    """Test the new password generation format"""
    print("\n" + "="*50)
    print("TESTING PASSWORD GENERATION")
    print("="*50)
    
    test_cases = [
        "CS2024001",
        "EE2023045",
        "ADMIN001",
        "LIB001"
    ]
    
    for user_id in test_cases:
        expected_password = f"{user_id}{user_id}"
        generated_password = User.generate_password(user_id)
        
        if generated_password == expected_password:
            print(f"✓ {user_id}: {generated_password}")
        else:
            print(f"✗ {user_id}: Expected {expected_password}, got {generated_password}")

def test_user_creation():
    """Test user creation with new password format"""
    print("\n" + "="*50)
    print("TESTING USER CREATION")
    print("="*50)
    
    with app.app_context():
        try:
            # Create a test user
            test_user_id = "TEST001"
            test_user = User(
                user_id=test_user_id,
                username=User.generate_username("Test User", test_user_id),
                name="Test User",
                email="<EMAIL>",
                role="student",
                designation="student",
                dob="2000-01-01",
                validity_date="2025-12-31",
                college_id=None,
                department_id=None
            )
            
            # Set password using new format
            password = User.generate_password(test_user_id)
            test_user.set_password(password)
            
            db.session.add(test_user)
            db.session.commit()
            
            print(f"✓ Test user created: {test_user_id}")
            print(f"✓ Generated password: {password}")
            
            # Test password verification
            if test_user.check_password(password):
                print("✓ Password verification successful")
            else:
                print("✗ Password verification failed")
            
            # Clean up
            db.session.delete(test_user)
            db.session.commit()
            print("✓ Test user cleaned up")
            
        except Exception as e:
            print(f"✗ User creation test failed: {str(e)}")
            db.session.rollback()

def print_api_endpoints():
    """Print the available API endpoints"""
    print("\n" + "="*50)
    print("AVAILABLE API ENDPOINTS")
    print("="*50)
    
    endpoints = [
        "GET  /api/admin/categories - Get all categories",
        "POST /api/admin/categories - Create new category",
        "PUT  /api/admin/categories/<id> - Update category",
        "DELETE /api/admin/categories/<id> - Delete category",
        "",
        "POST /api/auth/login - Login with user_id and password",
        "",
        "Example category creation:",
        "POST /api/admin/categories",
        "Body: {\"name\": \"Fiction\", \"description\": \"Fiction books\"}",
        "",
        "Example login:",
        "POST /api/auth/login", 
        "Body: {\"user_id\": \"CS2024001\", \"password\": \"CS2024001CS2024001\"}"
    ]
    
    for endpoint in endpoints:
        print(endpoint)

def main():
    """Run all tests"""
    print("🧪 LIBRARY MANAGEMENT SYSTEM - FIX VERIFICATION")
    print("Testing category creation, user authentication, and password generation...")
    
    test_password_generation()
    test_category_creation()
    test_user_creation()
    print_api_endpoints()
    
    print("\n" + "="*50)
    print("✅ ALL TESTS COMPLETED!")
    print("="*50)
    print("\nNext steps:")
    print("1. Start the Flask server: python app.py")
    print("2. Start the React client: cd ../client && npm start")
    print("3. Test the 'Add Category' functionality in the admin panel")
    print("4. Test login with User ID and new password format")

if __name__ == '__main__':
    main()
