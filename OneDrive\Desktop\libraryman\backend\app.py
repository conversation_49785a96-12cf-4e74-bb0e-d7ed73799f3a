from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity, get_jwt
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import text
import os
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///library.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)

# CORS configuration for localhost development
CORS(app,
     origins=["http://localhost:5173", "http://127.0.0.1:5173"],
     allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     supports_credentials=True)

migrate = Migrate(app, db)

# Define models inline
class College(db.Model):
    __tablename__ = 'colleges'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')
    users = db.relationship('User', backref='college', lazy=True)

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    users = db.relationship('User', backref='department', lazy=True)

    __table_args__ = (db.UniqueConstraint('name', 'college_id'),)

class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), nullable=False, unique=True)
    username = db.Column(db.String(80), nullable=False, unique=True)
    password_hash = db.Column(db.String(120), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False, unique=True)
    role = db.Column(db.String(20), nullable=False)
    user_role = db.Column(db.String(20), default='student')  # student, staff
    designation = db.Column(db.String(50), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    validity_date = db.Column(db.Date, nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    @staticmethod
    def generate_username(name, user_id):
        """Generate username from name + user_id"""
        # Remove spaces and special characters from name, take first part
        clean_name = ''.join(c for c in name.split()[0] if c.isalnum()).lower()
        return f"{clean_name}{user_id}"

    @staticmethod
    def generate_password(user_id):
        """Generate password using userid+userid format"""
        return f"{user_id}{user_id}"

    @staticmethod
    def generate_random_password(length=8):
        """Generate random password (legacy method)"""
        import string
        import random
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

# Book and other models (enhanced)
class Book(db.Model):
    __tablename__ = 'books'

    id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), nullable=False, unique=True)
    title = db.Column(db.String(200), nullable=False)
    # Multiple authors support
    author_1 = db.Column(db.String(200), nullable=False)  # Primary author (required)
    author_2 = db.Column(db.String(200), nullable=True)   # Optional
    author_3 = db.Column(db.String(200), nullable=True)   # Optional
    author_4 = db.Column(db.String(200), nullable=True)   # Optional
    # Legacy author field for backward compatibility
    author = db.Column(db.String(200), nullable=True)
    publisher = db.Column(db.String(100))
    department = db.Column(db.String(100))
    category = db.Column(db.String(50))
    location = db.Column(db.String(50))
    number_of_copies = db.Column(db.Integer, default=1)
    available_copies = db.Column(db.Integer, default=1)
    isbn = db.Column(db.String(20))
    # New mandatory fields
    pages = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False)  # Decimal field for price
    edition = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Ebook(db.Model):
    __tablename__ = 'ebooks'

    id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), nullable=False, unique=True)
    website = db.Column(db.String(500), nullable=False)  # Website URL
    web_detail = db.Column(db.Text, nullable=True)  # Website details/description
    web_title = db.Column(db.String(300), nullable=False)  # Website title
    subject = db.Column(db.String(200), nullable=False)  # Subject
    type = db.Column(db.String(50), nullable=False)  # E-journal, E-book, etc.
    download_count = db.Column(db.Integer, default=0)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='ebooks')

class QuestionBank(db.Model):
    __tablename__ = 'question_banks'

    id = db.Column(db.Integer, primary_key=True)
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    subject_name = db.Column(db.String(200), nullable=False)
    subject_code = db.Column(db.String(50), nullable=False)
    regulation = db.Column(db.String(100), nullable=True)  # Optional
    file_path = db.Column(db.String(500), nullable=False)
    file_name = db.Column(db.String(200), nullable=False)
    file_size = db.Column(db.String(20))
    download_count = db.Column(db.Integer, default=0)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    college = db.relationship('College', backref='question_banks')
    department = db.relationship('Department', backref='question_banks')
    uploader = db.relationship('User', backref='uploaded_qbs')

class Circulation(db.Model):
    __tablename__ = 'circulations'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    book_id = db.Column(db.Integer, db.ForeignKey('books.id'), nullable=False)
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='issued')  # issued, returned, overdue
    fine_amount = db.Column(db.Float, default=0.0)
    # Renewal tracking
    renewal_count = db.Column(db.Integer, default=0)
    max_renewals = db.Column(db.Integer, default=2)

    # Relationships
    user = db.relationship('User', backref='circulations')
    book = db.relationship('Book', backref='circulations')

    def can_renew(self):
        """Check if this circulation can be renewed"""
        if self.status != 'issued':
            return False, "Book is not currently issued"

        if (self.renewal_count or 0) >= (self.max_renewals or 2):
            return False, f"Maximum renewals ({self.max_renewals or 2}) reached"

        # Check if overdue
        from datetime import date
        if self.due_date.date() < date.today():
            return False, "Cannot renew overdue book"

        # Check if book is reserved by someone else (will be checked later when Reservation is defined)
        try:
            from sqlalchemy import text
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM reservations
                WHERE book_id = :book_id AND status = 'active'
            """), {"book_id": self.book_id}).scalar()

            if result and result > 0:
                return False, "Book is reserved by another user"
        except:
            # If reservations table doesn't exist yet, allow renewal
            pass

        return True, "Eligible for renewal"

class Reservation(db.Model):
    __tablename__ = 'reservations'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    book_id = db.Column(db.Integer, db.ForeignKey('books.id'), nullable=False)
    reservation_date = db.Column(db.DateTime, default=datetime.utcnow)
    expiry_date = db.Column(db.DateTime, nullable=False)  # When reservation expires
    notification_date = db.Column(db.DateTime)  # When user was notified book is available
    pickup_deadline = db.Column(db.DateTime)  # Deadline to pick up book (48 hours after notification)
    status = db.Column(db.String(20), default='active')  # active, fulfilled, expired, cancelled
    queue_position = db.Column(db.Integer)  # Position in reservation queue
    notes = db.Column(db.Text)  # Additional notes

    # Relationships
    user = db.relationship('User', backref='reservations')
    book = db.relationship('Book', backref='reservations')

    def calculate_estimated_availability(self):
        """Calculate estimated availability date based on current circulation"""
        current_circulation = Circulation.query.filter_by(
            book_id=self.book_id,
            status='issued'
        ).first()

        if current_circulation:
            return current_circulation.due_date
        else:
            # Book is available now
            return datetime.utcnow()

class Fine(db.Model):
    __tablename__ = 'fines'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    circulation_id = db.Column(db.Integer, db.ForeignKey('circulations.id'), nullable=True)
    amount = db.Column(db.Float, nullable=False)
    reason = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, paid
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    paid_date = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='fines')
    circulation = db.relationship('Circulation', backref='fines')
    created_by_user = db.relationship('User', foreign_keys=[created_by])

class NewsClipping(db.Model):
    __tablename__ = 'news_clippings'

    id = db.Column(db.Integer, primary_key=True)
    clipping_no = db.Column(db.String(50), nullable=False, unique=True)
    newspaper_name = db.Column(db.String(200), nullable=False)
    news_type = db.Column(db.String(100), nullable=False)  # e.g., Politics, Sports, Technology, etc.
    date = db.Column(db.Date, nullable=False)  # Date of the news
    pages = db.Column(db.String(50), nullable=False)  # Page numbers like "1-3" or "5"
    news_title = db.Column(db.String(300), nullable=False)
    news_subject = db.Column(db.String(200), nullable=False)
    keywords = db.Column(db.String(500), nullable=False)  # Comma-separated keywords
    pdf_file_name = db.Column(db.String(255), nullable=False)
    pdf_file_path = db.Column(db.String(500), nullable=False)
    pdf_file_size = db.Column(db.String(20))
    abstract = db.Column(db.Text, nullable=True)  # Optional
    content = db.Column(db.Text, nullable=True)  # Optional
    download_count = db.Column(db.Integer, default=0)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='news_clippings')

# Category model for book categorization
class Category(db.Model):
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

# Gate Entry Credentials model
class GateEntryCredential(db.Model):
    __tablename__ = 'gate_entry_credentials'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    created_by_user = db.relationship('User', backref='gate_credentials_created')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# Gate Entry Log model
class GateEntryLog(db.Model):
    __tablename__ = 'gate_entry_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    entry_time = db.Column(db.DateTime, nullable=True)
    exit_time = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(20), default='in')  # in, out
    scanned_by = db.Column(db.Integer, db.ForeignKey('gate_entry_credentials.id'), nullable=False)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='gate_logs')
    scanned_by_credential = db.relationship('GateEntryCredential', backref='scanned_logs')

# Note: Blueprint imports commented out due to circular import issues
# Will add routes directly to app for now
# from routes.admin import admin_bp
# from routes.auth import auth_bp
# from routes.student import student_bp
# from routes.librarian import librarian_bp

# Register blueprints
# app.register_blueprint(admin_bp, url_prefix='/api/admin')
# app.register_blueprint(auth_bp, url_prefix='/api/auth')
# app.register_blueprint(student_bp, url_prefix='/api/student')
# app.register_blueprint(librarian_bp, url_prefix='/api/librarian')

# Basic authentication route
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        # Support both user_id (new) and username (backward compatibility)
        user_id = data.get('user_id') or data.get('username')
        password = data.get('password')

        if not user_id or not password:
            return jsonify({'error': 'User ID and password required'}), 400

        # Try to find user by user_id first, then by username for backward compatibility
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            user = User.query.filter_by(username=user_id).first()

        if user and user.check_password(password) and user.is_active:
            # Check if user's validity date has not expired
            from datetime import date
            if user.validity_date < date.today():
                return jsonify({'error': 'Account has expired'}), 401

            access_token = create_access_token(
                identity=str(user.id),
                expires_delta=timedelta(hours=24)
            )

            return jsonify({
                'access_token': access_token,
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'email': user.email,
                    'role': user.role,
                    'designation': user.designation,
                    'college_id': user.college_id,
                    'department_id': user.department_id
                }
            }), 200

        return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'designation': user.designation,
                'dob': user.dob.isoformat() if user.dob else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'created_at': user.created_at.isoformat()
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Book Management Routes
@app.route('/api/admin/books', methods=['GET'])
@jwt_required()
def get_books():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')

        query = Book.query
        if search:
            query = query.filter(
                Book.title.contains(search) |
                Book.author.contains(search) |
                Book.access_no.contains(search)
            )

        books = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'books': [{
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                # Multiple authors
                'author_1': getattr(book, 'author_1', None) or book.author,
                'author_2': getattr(book, 'author_2', None),
                'author_3': getattr(book, 'author_3', None),
                'author_4': getattr(book, 'author_4', None),
                # Legacy author field for backward compatibility
                'author': book.author or getattr(book, 'author_1', None),
                'publisher': book.publisher,
                'department': book.department,
                'category': book.category,
                'location': book.location,
                'number_of_copies': book.number_of_copies,
                'available_copies': book.available_copies,
                'isbn': book.isbn,
                # New mandatory fields
                'pages': getattr(book, 'pages', None),
                'price': float(getattr(book, 'price', 0)) if getattr(book, 'price', None) else None,
                'edition': getattr(book, 'edition', None),
                'created_at': book.created_at.isoformat()
            } for book in books.items],
            'pagination': {
                'page': books.page,
                'pages': books.pages,
                'per_page': books.per_page,
                'total': books.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books', methods=['POST'])
@jwt_required()
def create_book():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()

        # Validate mandatory fields
        required_fields = ['title', 'author_1', 'pages', 'price', 'edition']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field.replace("_", " ").title()} is required'}), 400

        # Validate numeric fields
        try:
            pages = int(data.get('pages'))
            if pages <= 0:
                return jsonify({'error': 'Pages must be a positive number'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Pages must be a valid number'}), 400

        try:
            price = float(data.get('price'))
            if price < 0:
                return jsonify({'error': 'Price cannot be negative'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Price must be a valid number'}), 400

        try:
            number_of_copies = int(data.get('number_of_copies', 1))
            if number_of_copies <= 0:
                return jsonify({'error': 'Number of copies must be a positive number'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Number of copies must be a valid number'}), 400

        # Get the next available access number
        def get_next_access_number():
            # Find the highest numeric access number
            import re
            all_books = Book.query.all()
            max_number = 0

            for book in all_books:
                if book.access_no:
                    numbers = re.findall(r'\d+', book.access_no)
                    if numbers:
                        number = int(numbers[-1])
                        max_number = max(max_number, number)

            return f"B{max_number + 1:04d}"

        # Create multiple book records for multiple copies
        created_books = []
        base_access_no = data.get('access_no', '').strip()

        # Determine starting access number
        if base_access_no:
            # Check if provided access number already exists
            existing = Book.query.filter_by(access_no=base_access_no).first()
            if existing:
                return jsonify({'error': f'Access number {base_access_no} already exists'}), 400
            starting_access_no = base_access_no
        else:
            # Auto-generate starting access number
            starting_access_no = get_next_access_number()

        # Extract base number for sequential generation
        import re
        numbers = re.findall(r'\d+', starting_access_no)
        if numbers:
            base_number = int(numbers[-1])
            prefix = starting_access_no[:starting_access_no.rfind(str(base_number))]
        else:
            base_number = 1
            prefix = "B"

        for copy_index in range(number_of_copies):
            # Generate sequential access numbers
            current_number = base_number + copy_index
            access_no = f"{prefix}{current_number:04d}"

            # Double-check if access number already exists
            existing = Book.query.filter_by(access_no=access_no).first()
            if existing:
                return jsonify({'error': f'Access number {access_no} already exists'}), 400

            book = Book(
                access_no=access_no,
                title=data.get('title'),
                # Multiple authors
                author_1=data.get('author_1'),
                author_2=data.get('author_2') if data.get('author_2') else None,
                author_3=data.get('author_3') if data.get('author_3') else None,
                author_4=data.get('author_4') if data.get('author_4') else None,
                # Legacy author field for backward compatibility
                author=data.get('author_1'),  # Set to primary author
                publisher=data.get('publisher'),
                department=data.get('department'),
                category=data.get('category'),
                location=data.get('location'),
                number_of_copies=1,  # Each record represents one physical copy
                available_copies=1,  # Each copy is initially available
                isbn=data.get('isbn'),
                # New mandatory fields
                pages=pages,
                price=price,
                edition=data.get('edition')
            )

            db.session.add(book)
            created_books.append({
                'id': None,  # Will be set after commit
                'access_no': access_no,
                'title': book.title,
                'author': book.author_1
            })

        db.session.commit()

        # Update IDs after commit
        for i, book_info in enumerate(created_books):
            book_info['id'] = created_books[i]['id']

        return jsonify({
            'message': f'Successfully created {number_of_copies} book record(s)',
            'books_created': len(created_books),
            'books': created_books,
            'access_numbers': [book['access_no'] for book in created_books]
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books/bulk', methods=['POST'])
@jwt_required()
def bulk_create_books():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']

        # Read Excel file
        try:
            import pandas as pd
            df = pd.read_excel(file)
        except ImportError:
            return jsonify({'error': 'pandas library not installed'}), 500

        # Validate required columns
        required_columns = ['access_no', 'title', 'author_1', 'publisher', 'department', 'category', 'location', 'number_of_copies', 'pages', 'price', 'edition']
        optional_columns = ['author_2', 'author_3', 'author_4', 'isbn']

        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain required columns: {required_columns}'}), 400

        created_books = []
        errors = []

        for index, row in df.iterrows():
            try:
                access_no = str(row['access_no'])

                # Check if book already exists
                existing = Book.query.filter_by(access_no=access_no).first()
                if existing:
                    errors.append(f"Row {index + 1}: Access number {access_no} already exists")
                    continue

                # Validate mandatory fields
                if not row['author_1'] or pd.isna(row['author_1']):
                    errors.append(f"Row {index + 1}: Author 1 is required")
                    continue

                if not row['pages'] or pd.isna(row['pages']) or int(row['pages']) <= 0:
                    errors.append(f"Row {index + 1}: Pages must be a positive number")
                    continue

                if not row['price'] or pd.isna(row['price']) or float(row['price']) < 0:
                    errors.append(f"Row {index + 1}: Price cannot be negative")
                    continue

                if not row['edition'] or pd.isna(row['edition']):
                    errors.append(f"Row {index + 1}: Edition is required")
                    continue

                book = Book(
                    access_no=access_no,
                    title=row['title'],
                    # Multiple authors
                    author_1=row['author_1'],
                    author_2=row.get('author_2') if not pd.isna(row.get('author_2', '')) else None,
                    author_3=row.get('author_3') if not pd.isna(row.get('author_3', '')) else None,
                    author_4=row.get('author_4') if not pd.isna(row.get('author_4', '')) else None,
                    # Legacy author field for backward compatibility
                    author=row['author_1'],
                    publisher=row['publisher'],
                    department=row['department'],
                    category=row['category'],
                    location=row['location'],
                    number_of_copies=int(row['number_of_copies']),
                    available_copies=int(row['number_of_copies']),
                    isbn=row.get('isbn', ''),
                    # New mandatory fields
                    pages=int(row['pages']),
                    price=float(row['price']),
                    edition=row['edition']
                )

                db.session.add(book)
                created_books.append({
                    'access_no': access_no,
                    'title': row['title'],
                    'author': row['author']
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_books)} books',
            'created_books': created_books,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books/sample', methods=['GET'])
@jwt_required()
def download_books_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        import pandas as pd
        import io
        from flask import send_file

        # Create sample data with new fields
        sample_data = {
            'access_no': ['B001', 'B002', 'B003'],
            'title': ['Introduction to Computer Science', 'Data Structures and Algorithms', 'Database Management Systems'],
            'author_1': ['John Smith', 'Jane Doe', 'Robert Johnson'],
            'author_2': ['Mary Wilson', '', 'Sarah Davis'],
            'author_3': ['', '', ''],
            'author_4': ['', '', ''],
            'publisher': ['Tech Publications', 'Academic Press', 'University Books'],
            'department': ['Computer Science', 'Computer Science', 'Information Technology'],
            'category': ['Textbook', 'Reference', 'Textbook'],
            'location': ['A1-S1', 'A1-S2', 'A2-S1'],
            'number_of_copies': [5, 3, 4],
            'pages': [450, 320, 280],
            'price': [89.99, 75.50, 65.00],
            'edition': ['3rd Edition', '2nd Edition', '1st Edition'],
            'isbn': ['978-0123456789', '978-0987654321', '978-0456789123']
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Books Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='books_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Book
@app.route('/api/admin/books/<int:book_id>', methods=['PUT'])
@jwt_required()
def update_book(book_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        data = request.get_json()

        # Validate mandatory fields
        required_fields = ['access_no', 'title', 'author_1', 'pages', 'price', 'edition']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field.replace("_", " ").title()} is required'}), 400

        # Validate numeric fields
        try:
            pages = int(data.get('pages'))
            if pages <= 0:
                return jsonify({'error': 'Pages must be a positive number'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Pages must be a valid number'}), 400

        try:
            price = float(data.get('price'))
            if price < 0:
                return jsonify({'error': 'Price cannot be negative'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Price must be a valid number'}), 400

        access_no = data.get('access_no')
        title = data.get('title')
        author_1 = data.get('author_1')
        author_2 = data.get('author_2')
        author_3 = data.get('author_3')
        author_4 = data.get('author_4')
        publisher = data.get('publisher')
        department = data.get('department')
        category = data.get('category')
        location = data.get('location')
        number_of_copies = data.get('number_of_copies', 1)
        isbn = data.get('isbn')
        edition = data.get('edition')

        # Check if another book with same access number exists
        existing = Book.query.filter(Book.access_no == access_no, Book.id != book_id).first()
        if existing:
            return jsonify({'error': 'Access number already exists'}), 400

        # Calculate available copies change
        copies_diff = int(number_of_copies) - book.number_of_copies
        new_available = book.available_copies + copies_diff

        if new_available < 0:
            return jsonify({'error': 'Cannot reduce copies below issued books count'}), 400

        book.access_no = access_no
        book.title = title
        # Update multiple authors
        book.author_1 = author_1
        book.author_2 = author_2 if author_2 else None
        book.author_3 = author_3 if author_3 else None
        book.author_4 = author_4 if author_4 else None
        # Update legacy author field for backward compatibility
        book.author = author_1
        book.publisher = publisher
        book.department = department
        book.category = category
        book.location = location
        book.number_of_copies = int(number_of_copies)
        book.available_copies = new_available
        book.isbn = isbn
        # Update new mandatory fields
        book.pages = pages
        book.price = price
        book.edition = edition

        db.session.commit()

        return jsonify({
            'message': 'Book updated successfully',
            'book': {
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Book
@app.route('/api/admin/books/<int:book_id>', methods=['DELETE'])
@jwt_required()
def delete_book(book_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if book is currently issued
        if book.available_copies < book.number_of_copies:
            return jsonify({'error': 'Cannot delete book that is currently issued'}), 400

        db.session.delete(book)
        db.session.commit()

        return jsonify({'message': 'Book deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete All Books
@app.route('/api/admin/books/delete-all', methods=['DELETE'])
@jwt_required()
def delete_all_books():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin']:  # Only admin can delete all books
            return jsonify({'error': 'Admin access required for this operation'}), 403

        # Count total books before deletion
        total_books = Book.query.count()

        if total_books == 0:
            return jsonify({'message': 'No books to delete', 'deleted_count': 0}), 200

        # Check for active circulations
        active_circulations = Circulation.query.filter_by(status='issued').count()
        if active_circulations > 0:
            return jsonify({
                'error': f'Cannot delete all books. {active_circulations} books are currently issued. Please return all books before deleting.'
            }), 400

        # Delete all circulation history first (to maintain referential integrity)
        circulation_count = Circulation.query.count()
        if circulation_count > 0:
            Circulation.query.delete()

        # Delete all books
        Book.query.delete()

        # Commit the transaction
        db.session.commit()

        return jsonify({
            'message': f'Successfully deleted all books from the library',
            'deleted_count': total_books,
            'circulation_records_deleted': circulation_count
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to delete all books: {str(e)}'}), 500

# Student Book Details Route
@app.route('/api/student/books/<int:book_id>', methods=['GET'])
@jwt_required()
def get_book_details(book_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if user has borrowed this book
        user_circulation = Circulation.query.filter_by(
            user_id=user_id,
            book_id=book_id,
            status='issued'
        ).first()

        # Check if user has reserved this book
        user_reservation = Reservation.query.filter_by(
            user_id=user_id,
            book_id=book_id,
            status='active'
        ).first()

        # Get current circulation (if any)
        current_circulation = Circulation.query.filter_by(
            book_id=book_id,
            status='issued'
        ).first()

        # Get reservation queue
        reservation_queue = Reservation.query.filter_by(
            book_id=book_id,
            status='active'
        ).order_by(Reservation.queue_position).all()

        # Build book details response with safe field access
        book_details = {
            'id': book.id,
            'access_no': getattr(book, 'access_no', f'B{book.id:04d}'),
            'title': book.title,
            'author_1': getattr(book, 'author_1', None) or book.author,
            'author_2': getattr(book, 'author_2', None),
            'author_3': getattr(book, 'author_3', None),
            'author_4': getattr(book, 'author_4', None),
            'author': book.author,  # Legacy field
            'publisher': getattr(book, 'publisher', 'Unknown'),
            'department': getattr(book, 'department', 'General'),
            'category': getattr(book, 'category', 'General'),
            'location': getattr(book, 'location', 'Library'),
            'isbn': getattr(book, 'isbn', ''),
            'pages': getattr(book, 'pages', 100),
            'price': float(getattr(book, 'price', 0.0)) if getattr(book, 'price', None) else 0.0,
            'edition': getattr(book, 'edition', '1st Edition'),
            'description': getattr(book, 'description', ''),
            'number_of_copies': getattr(book, 'number_of_copies', 1),
            'available_copies': getattr(book, 'available_copies', 1),
            'created_at': book.created_at.isoformat() if hasattr(book, 'created_at') and book.created_at else datetime.utcnow().isoformat(),

            # User relationship to book
            'user_has_borrowed': user_circulation is not None,
            'user_has_reserved': user_reservation is not None,
            'user_queue_position': user_reservation.queue_position if user_reservation else None,

            # Current status
            'current_due_date': current_circulation.due_date.isoformat() if current_circulation else None,
            'reservation_queue_length': len(reservation_queue),

            # Renewal info if user has borrowed
            'circulation_id': user_circulation.id if user_circulation else None,
            'can_renew': False,
            'renewal_reason': None,
        }

        # Safely check renewal status
        if user_circulation:
            try:
                can_renew, reason = user_circulation.can_renew()
                book_details['can_renew'] = can_renew
                book_details['renewal_reason'] = reason
            except:
                book_details['can_renew'] = False
                book_details['renewal_reason'] = 'Unable to check renewal status'

        return jsonify({'book': book_details}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Admin/Librarian Reservation Management Routes
@app.route('/api/admin/reservations', methods=['GET'])
@jwt_required()
def get_all_reservations():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', 'active')

        # Get reservations with user and book information
        reservations_query = db.session.query(Reservation, User, Book).join(
            User, Reservation.user_id == User.id
        ).join(
            Book, Reservation.book_id == Book.id
        )

        if status != 'all':
            reservations_query = reservations_query.filter(Reservation.status == status)

        reservations = reservations_query.order_by(
            Reservation.reservation_date.desc()
        ).paginate(
            page=page, per_page=per_page, error_out=False
        )

        reservation_list = []
        for reservation, user, book in reservations.items:
            reservation_list.append({
                'id': reservation.id,
                'user': {
                    'id': user.id,
                    'name': user.name,
                    'user_id': user.user_id,
                    'email': user.email
                },
                'book': {
                    'id': book.id,
                    'title': book.title,
                    'author_1': book.author_1,
                    'access_no': book.access_no
                },
                'reservation_date': reservation.reservation_date.isoformat(),
                'expiry_date': reservation.expiry_date.isoformat(),
                'notification_date': reservation.notification_date.isoformat() if reservation.notification_date else None,
                'pickup_deadline': reservation.pickup_deadline.isoformat() if reservation.pickup_deadline else None,
                'status': reservation.status,
                'queue_position': reservation.queue_position,
                'notes': reservation.notes
            })

        return jsonify({
            'reservations': reservation_list,
            'pagination': {
                'page': reservations.page,
                'pages': reservations.pages,
                'per_page': reservations.per_page,
                'total': reservations.total
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/reservations/<int:reservation_id>/fulfill', methods=['POST'])
@jwt_required()
def fulfill_reservation(reservation_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        reservation = Reservation.query.get(reservation_id)
        if not reservation:
            return jsonify({'error': 'Reservation not found'}), 404

        if reservation.status != 'active':
            return jsonify({'error': 'Reservation is not active'}), 400

        book = Book.query.get(reservation.book_id)
        if book.available_copies <= 0:
            return jsonify({'error': 'No copies available'}), 400

        # Create circulation record
        from datetime import datetime, timedelta
        circulation = Circulation(
            user_id=reservation.user_id,
            book_id=reservation.book_id,
            due_date=datetime.now() + timedelta(days=14),
            status='issued',
            renewal_count=0,
            max_renewals=2
        )

        # Update book availability
        book.available_copies -= 1

        # Mark reservation as fulfilled
        reservation.status = 'fulfilled'

        # Update queue positions for other reservations
        other_reservations = Reservation.query.filter(
            Reservation.book_id == reservation.book_id,
            Reservation.status == 'active',
            Reservation.queue_position > reservation.queue_position
        ).all()

        for res in other_reservations:
            res.queue_position -= 1

        db.session.add(circulation)
        db.session.commit()

        return jsonify({
            'message': 'Reservation fulfilled successfully',
            'circulation_id': circulation.id
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/reservations/<int:reservation_id>/cancel', methods=['DELETE'])
@jwt_required()
def admin_cancel_reservation(reservation_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json() or {}
        reason = data.get('reason', 'Cancelled by admin')

        reservation = Reservation.query.get(reservation_id)
        if not reservation:
            return jsonify({'error': 'Reservation not found'}), 404

        if reservation.status != 'active':
            return jsonify({'error': 'Reservation is not active'}), 400

        # Update reservation
        reservation.status = 'cancelled'
        reservation.notes = f"{reservation.notes or ''}\nCancelled by admin: {reason}".strip()

        # Update queue positions for other reservations
        other_reservations = Reservation.query.filter(
            Reservation.book_id == reservation.book_id,
            Reservation.status == 'active',
            Reservation.queue_position > reservation.queue_position
        ).all()

        for res in other_reservations:
            res.queue_position -= 1

        db.session.commit()

        return jsonify({'message': 'Reservation cancelled successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Get Categories for Student Dashboard
@app.route('/api/student/categories', methods=['GET'])
@jwt_required()
def get_student_categories():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        # Get all categories created by admin
        categories = Category.query.all()

        category_list = []
        for category in categories:
            category_list.append({
                'id': category.id,
                'name': category.name,
                'description': category.description
            })

        return jsonify({'categories': category_list}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Category Management Routes
@app.route('/api/admin/categories', methods=['GET'])
@jwt_required()
def get_categories():
    try:
        categories = Category.query.order_by(Category.name).all()
        return jsonify({
            'categories': [{
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat() if category.created_at else None
            } for category in categories]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/categories', methods=['POST'])
@jwt_required()
def create_category():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({'error': 'Category name is required'}), 400

        # Check if category already exists (case-insensitive)
        existing_category = Category.query.filter(
            Category.name.ilike(name)
        ).first()

        if existing_category:
            return jsonify({'error': f'Category "{name}" already exists'}), 400

        # Create new category
        category = Category(
            name=name,
            description=description if description else None,
            created_by=user_id
        )

        db.session.add(category)
        db.session.commit()

        return jsonify({
            'message': 'Category created successfully',
            'category': {
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat()
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/categories/<int:category_id>', methods=['PUT'])
@jwt_required()
def update_category(category_id):
    try:
        category = Category.query.get_or_404(category_id)
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({'error': 'Category name is required'}), 400

        # Check if another category with this name exists
        existing_category = Category.query.filter(
            Category.name.ilike(name),
            Category.id != category_id
        ).first()

        if existing_category:
            return jsonify({'error': f'Category "{name}" already exists'}), 400

        category.name = name
        category.description = description if description else None

        db.session.commit()

        return jsonify({
            'message': 'Category updated successfully',
            'category': {
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat()
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/categories/<int:category_id>', methods=['DELETE'])
@jwt_required()
def delete_category(category_id):
    try:
        category = Category.query.get_or_404(category_id)

        # Check if category is being used by any books
        books_using_category = Book.query.filter_by(category=category.name).count()
        ebooks_using_category = Ebook.query.filter_by(category=category.name).count()

        if books_using_category > 0 or ebooks_using_category > 0:
            return jsonify({
                'error': f'Cannot delete category "{category.name}" as it is being used by {books_using_category + ebooks_using_category} book(s)'
            }), 400

        db.session.delete(category)
        db.session.commit()

        return jsonify({'message': 'Category deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Public OPAC API Routes (No Authentication Required)
@app.route('/api/books/search', methods=['GET'])
def public_books_search():
    """Public endpoint for OPAC book search"""
    try:
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        author = request.args.get('author', '')
        isbn = request.args.get('isbn', '')
        department = request.args.get('department', '')
        availability = request.args.get('availability', 'all')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # Limit to 100 per page

        # Build query
        query = Book.query

        # Apply filters
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                db.or_(
                    Book.title.ilike(search_filter),
                    Book.author.ilike(search_filter),
                    Book.access_no.ilike(search_filter),
                    Book.isbn.ilike(search_filter) if Book.isbn else False
                )
            )

        if category:
            query = query.filter(Book.category.ilike(f"%{category}%"))

        if author:
            query = query.filter(Book.author.ilike(f"%{author}%"))

        if isbn:
            query = query.filter(Book.isbn.ilike(f"%{isbn}%"))

        if department:
            query = query.filter(Book.department.ilike(f"%{department}%"))

        if availability == 'available':
            query = query.filter(Book.available_copies > 0)
        elif availability == 'unavailable':
            query = query.filter(Book.available_copies == 0)

        # Order by title
        query = query.order_by(Book.title)

        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        books = []
        for book in pagination.items:
            # Create authors list for display
            authors = [book.author_1] if book.author_1 else []
            if book.author_2:
                authors.append(book.author_2)
            if book.author_3:
                authors.append(book.author_3)
            if book.author_4:
                authors.append(book.author_4)

            books.append({
                'id': book.id,
                'title': book.title,
                # Multiple authors
                'author_1': book.author_1,
                'author_2': book.author_2,
                'author_3': book.author_3,
                'author_4': book.author_4,
                'authors': authors,  # Combined authors list for easy display
                # Legacy author field for backward compatibility
                'author': book.author or book.author_1,
                'publisher': book.publisher,
                'category': book.category,
                'department': book.department,
                'location': book.location,
                'access_no': book.access_no,
                'isbn': book.isbn,
                'number_of_copies': book.number_of_copies,
                'available_copies': book.available_copies,
                # New fields
                'pages': book.pages,
                'price': float(book.price) if book.price else None,
                'edition': book.edition,
                'created_at': book.created_at.isoformat() if book.created_at else None
            })

        return jsonify({
            'books': books,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/categories/public', methods=['GET'])
def public_categories():
    """Public endpoint for OPAC categories"""
    try:
        categories = Category.query.order_by(Category.name).all()
        return jsonify({
            'categories': [{
                'id': category.id,
                'name': category.name,
                'description': category.description
            } for category in categories]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Basic admin routes for testing
@app.route('/api/admin/colleges', methods=['GET'])
@jwt_required()
def get_admin_colleges():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        colleges = College.query.all()
        return jsonify({
            'success': True,
            'colleges': [{
                'id': college.id,
                'name': college.name,
                'code': college.code,
                'created_at': college.created_at.isoformat() if college.created_at else None,
                'departments_count': len(college.departments) if college.departments else 0
            } for college in colleges]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/colleges', methods=['POST'])
@jwt_required()
def create_college():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')

        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if college already exists
        existing = College.query.filter_by(name=name).first()
        if existing:
            return jsonify({'error': 'College already exists'}), 400

        college = College(name=name, code=code)
        db.session.add(college)
        db.session.commit()

        return jsonify({
            'message': 'College created successfully',
            'college': {
                'id': college.id,
                'name': college.name,
                'code': college.code
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update College
@app.route('/api/admin/colleges/<int:college_id>', methods=['PUT'])
@jwt_required()
def update_college(college_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')

        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if another college with same name exists
        existing = College.query.filter(College.name == name, College.id != college_id).first()
        if existing:
            return jsonify({'error': 'College name already exists'}), 400

        # Check if another college with same code exists
        existing_code = College.query.filter(College.code == code, College.id != college_id).first()
        if existing_code:
            return jsonify({'error': 'College code already exists'}), 400

        college.name = name
        college.code = code
        db.session.commit()

        return jsonify({
            'message': 'College updated successfully',
            'college': {
                'id': college.id,
                'name': college.name,
                'code': college.code
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete College
@app.route('/api/admin/colleges/<int:college_id>', methods=['DELETE'])
@jwt_required()
def delete_college(college_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if college has departments
        if college.departments:
            return jsonify({'error': 'Cannot delete college with existing departments'}), 400

        # Check if college has users
        if college.users:
            return jsonify({'error': 'Cannot delete college with existing users'}), 400

        db.session.delete(college)
        db.session.commit()

        return jsonify({'message': 'College deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/departments', methods=['GET'])
@jwt_required()
def get_departments():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college_id = request.args.get('college_id')
        if college_id:
            departments = Department.query.filter_by(college_id=college_id).all()
        else:
            departments = Department.query.all()

        return jsonify({
            'departments': [{
                'id': dept.id,
                'name': dept.name,
                'code': dept.code,
                'college_id': dept.college_id,
                'college_name': dept.college.name,
                'created_at': dept.created_at.isoformat()
            } for dept in departments]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/departments', methods=['POST'])
@jwt_required()
def create_department():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        college_id = data.get('college_id')

        if not name or not code or not college_id:
            return jsonify({'error': 'Name, code, and college_id are required'}), 400

        # Check if college exists
        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if department already exists in this college
        existing = Department.query.filter_by(name=name, college_id=college_id).first()
        if existing:
            return jsonify({'error': 'Department already exists in this college'}), 400

        department = Department(name=name, code=code, college_id=college_id)
        db.session.add(department)
        db.session.commit()

        return jsonify({
            'message': 'Department created successfully',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Department
@app.route('/api/admin/departments/<int:department_id>', methods=['PUT'])
@jwt_required()
def update_department(department_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        department = Department.query.get(department_id)
        if not department:
            return jsonify({'error': 'Department not found'}), 404

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        college_id = data.get('college_id')

        if not name or not code or not college_id:
            return jsonify({'error': 'Name, code, and college are required'}), 400

        # Check if college exists
        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if another department with same name exists in the same college
        existing = Department.query.filter(
            Department.name == name,
            Department.college_id == college_id,
            Department.id != department_id
        ).first()
        if existing:
            return jsonify({'error': 'Department name already exists in this college'}), 400

        department.name = name
        department.code = code
        department.college_id = college_id
        db.session.commit()

        return jsonify({
            'message': 'Department updated successfully',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Department
@app.route('/api/admin/departments/<int:department_id>', methods=['DELETE'])
@jwt_required()
def delete_department(department_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        department = Department.query.get(department_id)
        if not department:
            return jsonify({'error': 'Department not found'}), 404

        # Check if department has users
        if department.users:
            return jsonify({'error': 'Cannot delete department with existing users'}), 400

        db.session.delete(department)
        db.session.commit()

        return jsonify({'message': 'Department deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# User Management Routes
@app.route('/api/admin/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        role = request.args.get('role')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')

        query = User.query
        if role:
            query = query.filter_by(role=role)
        if search:
            query = query.filter(
                User.name.contains(search) |
                User.email.contains(search) |
                User.user_id.contains(search)
            )

        users = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'users': [{
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'designation': user.designation,
                'dob': user.dob.isoformat() if user.dob else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat()
            } for user in users.items],
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/users', methods=['POST'])
@jwt_required()
def create_user():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        name = data.get('name')
        email = data.get('email')
        college_id = data.get('college_id')
        department_id = data.get('department_id')
        designation = data.get('designation')  # student or staff
        user_role = data.get('user_role', 'student')  # student or staff
        dob = data.get('dob')
        validity_date = data.get('validity_date')
        role = data.get('role', 'student')  # Default to student
        custom_username = data.get('username')  # Custom username for librarians
        custom_password = data.get('password')  # Custom password for librarians

        if not all([user_id, name, email, designation, dob, validity_date]):
            return jsonify({'error': 'All fields are required'}), 400

        # Check if user already exists
        existing = User.query.filter_by(user_id=user_id).first()
        if existing:
            return jsonify({'error': 'User ID already exists'}), 400

        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 400

        # Use custom username if provided (for librarians), otherwise use email
        if custom_username:
            username = custom_username
            # Check if username already exists
            existing_username = User.query.filter_by(username=username).first()
            if existing_username:
                return jsonify({'error': 'Username already exists'}), 400
        else:
            username = email  # Username should be email address

        # Use custom password if provided (for librarians), otherwise generate using userid+userid format
        if custom_password:
            password = custom_password
        else:
            password = User.generate_password(user_id)  # Password format: userid+userid

        # Parse dates
        dob_date = datetime.strptime(dob, '%Y-%m-%d').date()
        validity_date_obj = datetime.strptime(validity_date, '%Y-%m-%d').date()

        user = User(
            user_id=user_id,
            username=username,
            name=name,
            email=email,
            role=role,
            user_role=user_role,
            designation=designation,
            dob=dob_date,
            validity_date=validity_date_obj,
            college_id=college_id,
            department_id=department_id
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User created successfully',
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'username': username,
                'password': password,  # Return password for admin to share
                'name': user.name,
                'email': user.email,
                'role': user.role
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Bulk User Upload
@app.route('/api/admin/users/bulk', methods=['POST'])
@jwt_required()
def bulk_create_users():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        college_id = request.form.get('college_id')
        department_id = request.form.get('department_id')
        user_role = request.form.get('user_role', 'student')

        if not college_id or not department_id:
            return jsonify({'error': 'College and department are required'}), 400

        # Read Excel file
        import pandas as pd
        df = pd.read_excel(file)

        # Validate required columns
        required_columns = ['user_id', 'name', 'email', 'validity_date', 'dob']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_users = []
        errors = []

        for index, row in df.iterrows():
            try:
                user_id = str(row['user_id'])
                name = row['name']
                email = row['email']
                dob = pd.to_datetime(row['dob']).date()
                validity_date = pd.to_datetime(row['validity_date']).date()

                # Check if user already exists
                existing = User.query.filter_by(user_id=user_id).first()
                if existing:
                    errors.append(f"Row {index + 1}: User ID {user_id} already exists")
                    continue

                # Generate username and password according to requirements
                # Username should be the email address
                username = email
                # Password should be userid+userid format
                password = User.generate_password(user_id)

                user = User(
                    user_id=user_id,
                    username=username,
                    name=name,
                    email=email,
                    role='student',
                    user_role=user_role,
                    designation=user_role,
                    dob=dob,
                    validity_date=validity_date,
                    college_id=college_id,
                    department_id=department_id
                )
                user.set_password(password)

                db.session.add(user)
                created_users.append({
                    'user_id': user_id,
                    'username': username,
                    'password': password,
                    'name': name,
                    'email': email
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_users)} users',
            'created_users': created_users,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Download Credentials
@app.route('/api/admin/users/credentials', methods=['POST'])
@jwt_required()
def download_credentials():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        users_data = data.get('users', [])

        if not users_data:
            return jsonify({'error': 'No user data provided'}), 400

        # Create DataFrame with required columns: Student ID, Name, Email, Username, Password
        import pandas as pd
        import io
        from flask import send_file

        # Reformat data to match required columns
        formatted_data = []
        for user in users_data:
            formatted_data.append({
                'Student ID': user['user_id'],
                'Name': user['name'],
                'Email': user['email'],
                'Username': user['username'],
                'Password': user['password']
            })

        df = pd.DataFrame(formatted_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Student Credentials', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'student_credentials_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Circulation Management Routes

# Get user circulation info (for issue/return forms)
@app.route('/api/admin/circulation/user/<user_id>', methods=['GET'])
@jwt_required()
def get_user_circulation_info(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Find user by user_id (roll number) or database id
        print(f"Searching for user with user_id: {user_id}")
        user = User.query.filter_by(user_id=user_id).first()
        print(f"Found user by user_id: {user}")

        if not user:
            # Try to find by database id if user_id search failed
            try:
                user = User.query.get(int(user_id))
                print(f"Found user by database id: {user}")
            except (ValueError, TypeError):
                print(f"Could not convert {user_id} to int for database id search")

        if not user:
            # Debug: Show available users
            all_users = User.query.filter_by(role='student').limit(5).all()
            available_user_ids = [u.user_id for u in all_users]
            print(f"Available student user_ids: {available_user_ids}")
            return jsonify({
                'error': 'User not found',
                'searched_for': user_id,
                'available_student_ids': available_user_ids
            }), 404

        # Get current borrowed books
        current_books = db.session.query(Circulation, Book).join(Book).filter(
            Circulation.user_id == user.id,
            Circulation.status == 'issued'
        ).all()

        # Get borrowing history
        history = db.session.query(Circulation, Book).join(Book).filter(
            Circulation.user_id == user.id,
            Circulation.status.in_(['returned', 'overdue'])
        ).order_by(Circulation.issue_date.desc()).limit(10).all()

        # Calculate current fine amount
        total_fine = db.session.query(db.func.sum(Fine.amount)).filter(
            Fine.user_id == user.id,
            Fine.status == 'pending'
        ).scalar() or 0

        # Calculate overdue fines for current books
        from datetime import date
        today = date.today()
        daily_fine_rate = 1.0  # ₹1 per day

        current_books_data = []
        for circulation, book in current_books:
            days_overdue = 0
            is_overdue = False
            if circulation.due_date.date() < today:
                days_overdue = (today - circulation.due_date.date()).days
                is_overdue = True
                # Update circulation status if overdue
                if circulation.status != 'overdue':
                    circulation.status = 'overdue'
                    db.session.commit()

            current_books_data.append({
                'circulation_id': circulation.id,
                'book_id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'is_overdue': is_overdue,
                'days_overdue': days_overdue,
                'fine_amount': days_overdue * daily_fine_rate if is_overdue else 0
            })

        history_data = []
        for circulation, book in history:
            history_data.append({
                'book_title': book.title,
                'author': book.author,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'return_date': circulation.return_date.isoformat() if circulation.return_date else None,
                'status': circulation.status,
                'fine_amount': circulation.fine_amount
            })

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'current_books': current_books_data,
            'borrowing_history': history_data,
            'total_fine': total_fine,
            'can_borrow': total_fine == 0  # Can't borrow if there are outstanding fines
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Issue Book
@app.route('/api/admin/circulation/issue', methods=['POST'])
@jwt_required()
def issue_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        book_id = data.get('book_id')
        due_date = data.get('due_date')

        if not all([user_id, book_id, due_date]):
            return jsonify({'error': 'User ID, Book ID, and Due Date are required'}), 400

        # Find user
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user has outstanding fines
        outstanding_fines = db.session.query(db.func.sum(Fine.amount)).filter(
            Fine.user_id == user.id,
            Fine.status == 'pending'
        ).scalar() or 0

        if outstanding_fines > 0:
            return jsonify({'error': f'User has outstanding fines of ₹{outstanding_fines:.2f}. Please clear fines before issuing books.'}), 400

        # Find book
        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if book is available
        if book.available_copies <= 0:
            return jsonify({'error': 'Book is not available for issue'}), 400

        # Check for active reservations
        active_reservations = db.session.query(Reservation, User).join(
            User, Reservation.user_id == User.id
        ).filter(
            Reservation.book_id == book.id,
            Reservation.status == 'active'
        ).order_by(Reservation.queue_position).all()

        if active_reservations:
            # Check if the user trying to issue is the first in queue
            first_reservation, first_reserved_user = active_reservations[0]

            if first_reserved_user.id != user.id:
                return jsonify({
                    'error': 'RESERVATION_CONFLICT',
                    'message': f'This book is currently reserved by {first_reserved_user.name} (ID: {first_reserved_user.user_id}). Please contact the reserved student first or cancel their reservation before issuing to another student.',
                    'reservation_details': {
                        'student_name': first_reserved_user.name,
                        'student_id': first_reserved_user.user_id,
                        'student_email': first_reserved_user.email,
                        'reservation_date': first_reservation.reservation_date.isoformat(),
                        'queue_position': first_reservation.queue_position,
                        'total_reservations': len(active_reservations)
                    }
                }), 409  # Conflict status code
            else:
                # User is the first in queue, fulfill the reservation
                first_reservation.status = 'fulfilled'
                # Update queue positions for remaining reservations
                for reservation, _ in active_reservations[1:]:
                    reservation.queue_position -= 1

        # Create circulation record
        circulation = Circulation(
            user_id=user.id,
            book_id=book.id,
            due_date=datetime.strptime(due_date, '%Y-%m-%d'),
            status='issued'
        )

        # Update book availability
        book.available_copies -= 1

        db.session.add(circulation)
        db.session.commit()

        return jsonify({
            'message': 'Book issued successfully',
            'circulation': {
                'id': circulation.id,
                'user_name': user.name,
                'book_title': book.title,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat()
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Return Book
@app.route('/api/admin/circulation/return', methods=['POST'])
@jwt_required()
def return_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        circulation_ids = data.get('circulation_ids', [])

        if not circulation_ids:
            return jsonify({'error': 'No circulation IDs provided'}), 400

        returned_books = []
        total_fine = 0
        daily_fine_rate = 1.0  # $1 per day

        for circulation_id in circulation_ids:
            circulation = Circulation.query.get(circulation_id)
            if not circulation:
                continue

            if circulation.status != 'issued' and circulation.status != 'overdue':
                continue

            # Calculate fine if overdue
            from datetime import date
            today = date.today()
            fine_amount = 0

            if circulation.due_date.date() < today:
                days_overdue = (today - circulation.due_date.date()).days
                fine_amount = days_overdue * daily_fine_rate

            # Update circulation
            circulation.return_date = datetime.utcnow()
            circulation.status = 'returned'
            circulation.fine_amount = fine_amount

            # Update book availability
            book = Book.query.get(circulation.book_id)
            if book:
                book.available_copies += 1

            # Create fine record if applicable
            if fine_amount > 0:
                fine = Fine(
                    user_id=circulation.user_id,
                    circulation_id=circulation.id,
                    amount=fine_amount,
                    reason=f'Overdue fine for book: {book.title}',
                    status='pending',
                    created_by=current_user_id
                )
                db.session.add(fine)

            returned_books.append({
                'circulation_id': circulation.id,
                'book_title': book.title if book else 'Unknown',
                'fine_amount': fine_amount
            })
            total_fine += fine_amount

        db.session.commit()

        return jsonify({
            'message': f'Successfully returned {len(returned_books)} books',
            'returned_books': returned_books,
            'total_fine': total_fine
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Book Search for Issue
@app.route('/api/admin/books/search', methods=['GET'])
@jwt_required()
def search_books_for_issue():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        search = request.args.get('search', '')
        if len(search) < 2:
            return jsonify({'books': []}), 200

        books = Book.query.filter(
            db.or_(
                Book.title.contains(search),
                Book.author.contains(search),
                Book.access_no.contains(search)
            ),
            Book.available_copies > 0
        ).limit(10).all()

        return jsonify({
            'books': [{
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'available_copies': book.available_copies
            } for book in books]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Debug endpoint to check available books
@app.route('/api/admin/books/debug', methods=['GET'])
@jwt_required()
def debug_books():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        total_books = Book.query.count()
        available_books = Book.query.filter(Book.available_copies > 0).count()
        sample_books = Book.query.limit(5).all()

        return jsonify({
            'total_books': total_books,
            'available_books': available_books,
            'sample_books': [{
                'id': book.id,
                'title': book.title,
                'author': book.author,
                'access_no': book.access_no,
                'available_copies': book.available_copies
            } for book in sample_books]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Create sample books for testing
@app.route('/api/admin/books/create-samples', methods=['POST'])
@jwt_required()
def create_sample_books():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Check if books already exist
        existing_books = Book.query.count()
        if existing_books > 0:
            return jsonify({'message': f'Books already exist ({existing_books} books found)'}), 200

        # Create sample books
        sample_books = [
            {
                'access_no': 'BK001',
                'title': 'Introduction to Computer Science',
                'author': 'John Smith',
                'isbn': '978-0123456789',
                'category': 'Technology',
                'total_copies': 5,
                'available_copies': 5
            },
            {
                'access_no': 'BK002',
                'title': 'Data Structures and Algorithms',
                'author': 'Jane Doe',
                'isbn': '978-0987654321',
                'category': 'Technology',
                'total_copies': 3,
                'available_copies': 3
            },
            {
                'access_no': 'BK003',
                'title': 'Modern Web Development',
                'author': 'Bob Johnson',
                'isbn': '978-0456789123',
                'category': 'Technology',
                'total_copies': 4,
                'available_copies': 4
            },
            {
                'access_no': 'BK004',
                'title': 'Database Management Systems',
                'author': 'Alice Brown',
                'isbn': '978-0789123456',
                'category': 'Technology',
                'total_copies': 2,
                'available_copies': 2
            },
            {
                'access_no': 'BK005',
                'title': 'Software Engineering Principles',
                'author': 'Charlie Wilson',
                'isbn': '978-0321654987',
                'category': 'Technology',
                'total_copies': 3,
                'available_copies': 3
            }
        ]

        created_books = []
        for book_data in sample_books:
            book = Book(**book_data)
            db.session.add(book)
            created_books.append(book_data['title'])

        db.session.commit()

        return jsonify({
            'message': f'Created {len(sample_books)} sample books',
            'books': created_books
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Debug endpoint to check users in database
@app.route('/api/admin/users/debug', methods=['GET'])
@jwt_required()
def debug_users():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        total_users = User.query.count()
        students = User.query.filter_by(role='student').limit(10).all()

        return jsonify({
            'total_users': total_users,
            'total_students': User.query.filter_by(role='student').count(),
            'sample_students': [{
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            } for user in students]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Create sample students for testing
@app.route('/api/admin/users/create-sample-students', methods=['POST'])
@jwt_required()
def create_sample_students():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Check if students already exist
        existing_students = User.query.filter_by(role='student').count()
        if existing_students > 0:
            return jsonify({'message': f'Students already exist ({existing_students} students found)'}), 200

        # Get or create default college and department
        college = College.query.first()
        if not college:
            college = College(name='Default College', address='Default Address')
            db.session.add(college)
            db.session.flush()

        department = Department.query.first()
        if not department:
            department = Department(name='Computer Science', college_id=college.id)
            db.session.add(department)
            db.session.flush()

        # Create sample students
        sample_students = [
            {
                'user_id': 'ST001',
                'name': 'John Doe',
                'email': '<EMAIL>',
                'role': 'student',
                'college_id': college.id,
                'department_id': department.id,
                'validity_date': datetime(2025, 12, 31)
            },
            {
                'user_id': 'ST002',
                'name': 'Jane Smith',
                'email': '<EMAIL>',
                'role': 'student',
                'college_id': college.id,
                'department_id': department.id,
                'validity_date': datetime(2025, 12, 31)
            },
            {
                'user_id': 'ST003',
                'name': 'Bob Johnson',
                'email': '<EMAIL>',
                'role': 'student',
                'college_id': college.id,
                'department_id': department.id,
                'validity_date': datetime(2025, 12, 31)
            },
            {
                'user_id': 'ST004',
                'name': 'Alice Brown',
                'email': '<EMAIL>',
                'role': 'student',
                'college_id': college.id,
                'department_id': department.id,
                'validity_date': datetime(2025, 12, 31)
            },
            {
                'user_id': 'ST005',
                'name': 'Charlie Wilson',
                'email': '<EMAIL>',
                'role': 'student',
                'college_id': college.id,
                'department_id': department.id,
                'validity_date': datetime(2025, 12, 31)
            }
        ]

        created_students = []
        for student_data in sample_students:
            student = User(**student_data)
            student.set_password('password123')  # Default password
            db.session.add(student)
            created_students.append(student_data['user_id'])

        db.session.commit()

        return jsonify({
            'message': f'Created {len(sample_students)} sample students',
            'students': created_students,
            'default_password': 'password123'
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Get next access number for books
@app.route('/api/admin/books/next-access-number', methods=['GET'])
@jwt_required()
def get_next_access_number():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get the highest access number
        last_book = Book.query.order_by(Book.id.desc()).first()

        if not last_book or not last_book.access_no:
            next_number = "1"
        else:
            # Try to extract number from access_no
            import re
            numbers = re.findall(r'\d+', last_book.access_no)
            if numbers:
                last_number = int(numbers[-1])
                next_number = str(last_number + 1)
            else:
                next_number = "1"

        return jsonify({'next_access_number': next_number}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Renew Book
@app.route('/api/admin/circulation/renew', methods=['POST'])
@jwt_required()
def renew_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        circulation_ids = data.get('circulation_ids', [])
        renewal_days = data.get('renewal_days', 14)  # Default 14 days

        if not circulation_ids:
            return jsonify({'error': 'No circulation IDs provided'}), 400

        renewed_books = []

        for circulation_id in circulation_ids:
            circulation = Circulation.query.get(circulation_id)
            if not circulation:
                continue

            if circulation.status != 'issued':
                continue

            # Check if user has outstanding fines
            user_fines = db.session.query(db.func.sum(Fine.amount)).filter(
                Fine.user_id == circulation.user_id,
                Fine.status == 'pending'
            ).scalar() or 0

            if user_fines > 0:
                continue  # Skip renewal if user has outstanding fines

            # Check renewal count (you might want to add a renewal_count field to Circulation model)
            # For now, we'll allow unlimited renewals

            # Extend due date
            from datetime import timedelta
            circulation.due_date = circulation.due_date + timedelta(days=renewal_days)

            # Get book info
            book = Book.query.get(circulation.book_id)

            renewed_books.append({
                'circulation_id': circulation.id,
                'book_title': book.title if book else 'Unknown',
                'new_due_date': circulation.due_date.isoformat(),
                'renewal_days': renewal_days
            })

        db.session.commit()

        return jsonify({
            'message': f'Successfully renewed {len(renewed_books)} books',
            'renewed_books': renewed_books
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Circulation History Routes
@app.route('/api/admin/circulation/history', methods=['GET'])
@jwt_required()
def get_circulation_history():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Unauthorized'}), 403

        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        sort_field = request.args.get('sort_field', 'issue_date')
        sort_direction = request.args.get('sort_direction', 'desc')

        # Get filter parameters
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        status = request.args.get('status', 'all')
        user_type = request.args.get('user_type', 'all')
        college_id = request.args.get('college_id', 'all')
        department_id = request.args.get('department_id', 'all')

        # Build query
        query = db.session.query(Circulation, Book, User).join(
            Book, Circulation.book_id == Book.id
        ).join(
            User, Circulation.user_id == User.id
        )

        # Apply filters
        if from_date:
            query = query.filter(Circulation.issue_date >= datetime.strptime(from_date, '%Y-%m-%d'))
        if to_date:
            query = query.filter(Circulation.issue_date <= datetime.strptime(to_date, '%Y-%m-%d'))
        if status != 'all':
            if status == 'overdue':
                query = query.filter(
                    Circulation.status == 'issued',
                    Circulation.due_date < datetime.now()
                )
            else:
                query = query.filter(Circulation.status == status)
        if user_type != 'all':
            query = query.filter(User.role == user_type)
        if college_id != 'all':
            query = query.filter(User.college_id == int(college_id))
        if department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        # Apply sorting
        if sort_field == 'user_name':
            sort_column = User.name
        elif sort_field == 'book_title':
            sort_column = Book.title
        elif sort_field == 'issue_date':
            sort_column = Circulation.issue_date
        elif sort_field == 'due_date':
            sort_column = Circulation.due_date
        elif sort_field == 'return_date':
            sort_column = Circulation.return_date
        elif sort_field == 'status':
            sort_column = Circulation.status
        elif sort_field == 'fine_amount':
            sort_column = Circulation.fine_amount
        else:
            sort_column = Circulation.issue_date

        if sort_direction == 'desc':
            sort_column = sort_column.desc()

        query = query.order_by(sort_column)

        # Get paginated results
        total = query.count()
        results = query.offset((page - 1) * limit).limit(limit).all()

        # Format response data
        history_data = []
        for circulation, book, user in results:
            # Calculate status and fine
            status = circulation.status
            fine_amount = circulation.fine_amount or 0

            if status == 'issued' and circulation.due_date < datetime.now():
                status = 'overdue'
                # Calculate overdue fine if not already set
                if fine_amount == 0:
                    days_overdue = (datetime.now() - circulation.due_date).days
                    fine_amount = days_overdue * 5.0  # ₹5 per day

            history_data.append({
                'id': circulation.id,
                'user_name': user.name,
                'user_id': user.user_id,
                'user_type': user.role,
                'book_title': book.title,
                'book_author': book.author,
                'isbn': book.isbn,
                'book_id': book.id,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'return_date': circulation.return_date.isoformat() if circulation.return_date else None,
                'status': status,
                'fine_amount': fine_amount
            })

        return jsonify({
            'data': history_data,
            'total': total,
            'page': page,
            'limit': limit,
            'pages': (total + limit - 1) // limit
        }), 200

    except Exception as e:
        print(f"Circulation history error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/circulation/statistics', methods=['GET'])
@jwt_required()
def get_circulation_statistics():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Unauthorized'}), 403

        # Get total transactions
        total_transactions = Circulation.query.count()

        # Get active loans
        active_loans = Circulation.query.filter_by(status='issued').count()

        # Get overdue books
        overdue_books = Circulation.query.filter(
            Circulation.status == 'issued',
            Circulation.due_date < datetime.now()
        ).count()

        # Get total fines
        total_fines = db.session.query(db.func.sum(Circulation.fine_amount)).scalar() or 0

        # Get active users (users with current loans)
        active_users = db.session.query(Circulation.user_id).filter_by(status='issued').distinct().count()

        # Get popular books
        popular_books = db.session.query(
            Book.title,
            db.func.count(Circulation.id).label('count')
        ).join(
            Circulation, Book.id == Circulation.book_id
        ).group_by(
            Book.id, Book.title
        ).order_by(
            db.func.count(Circulation.id).desc()
        ).limit(5).all()

        return jsonify({
            'total_transactions': total_transactions,
            'active_loans': active_loans,
            'overdue_books': overdue_books,
            'total_fines': float(total_fines),
            'active_users': active_users,
            'popular_books': [{'title': book.title, 'count': book.count} for book in popular_books]
        }), 200

    except Exception as e:
        print(f"Statistics error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/circulation/export/excel', methods=['GET'])
@jwt_required()
def export_circulation_excel():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Unauthorized'}), 403

        # Get filter parameters (same as history endpoint)
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        status = request.args.get('status', 'all')
        user_type = request.args.get('user_type', 'all')
        college_id = request.args.get('college_id', 'all')
        department_id = request.args.get('department_id', 'all')

        # Build query (same as history endpoint)
        query = db.session.query(Circulation, Book, User).join(
            Book, Circulation.book_id == Book.id
        ).join(
            User, Circulation.user_id == User.id
        )

        # Apply filters
        if from_date:
            query = query.filter(Circulation.issue_date >= datetime.strptime(from_date, '%Y-%m-%d'))
        if to_date:
            query = query.filter(Circulation.issue_date <= datetime.strptime(to_date, '%Y-%m-%d'))
        if status != 'all':
            if status == 'overdue':
                query = query.filter(
                    Circulation.status == 'issued',
                    Circulation.due_date < datetime.now()
                )
            else:
                query = query.filter(Circulation.status == status)
        if user_type != 'all':
            query = query.filter(User.role == user_type)
        if college_id != 'all':
            query = query.filter(User.college_id == int(college_id))
        if department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        results = query.all()

        # Create CSV content
        import io
        output = io.StringIO()

        # Write headers
        headers = ['User Name', 'User ID', 'User Type', 'Book Title', 'Author', 'ISBN', 'Issue Date', 'Due Date', 'Return Date', 'Status', 'Fine Amount']
        output.write(','.join(headers) + '\n')

        # Write data
        for circulation, book, user in results:
            status = circulation.status
            fine_amount = circulation.fine_amount or 0

            if status == 'issued' and circulation.due_date < datetime.now():
                status = 'overdue'
                if fine_amount == 0:
                    days_overdue = (datetime.now() - circulation.due_date).days
                    fine_amount = days_overdue * 5.0

            row = [
                f'"{user.name}"',
                f'"{user.user_id}"',
                f'"{user.role}"',
                f'"{book.title}"',
                f'"{book.author}"',
                f'"{book.isbn}"',
                f'"{circulation.issue_date.strftime("%Y-%m-%d")}"',
                f'"{circulation.due_date.strftime("%Y-%m-%d")}"',
                f'"{circulation.return_date.strftime("%Y-%m-%d") if circulation.return_date else "N/A"}"',
                f'"{status}"',
                f'"₹{fine_amount:.2f}"'
            ]
            output.write(','.join(row) + '\n')

        # Create response
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename=circulation_history_{datetime.now().strftime("%Y%m%d")}.csv'

        return response

    except Exception as e:
        print(f"Export error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/circulation/export/pdf', methods=['GET'])
@jwt_required()
def export_circulation_pdf():
    try:
        # For now, return the same CSV format with PDF headers
        # In production, you would use a library like reportlab to generate actual PDF
        return export_circulation_excel()
    except Exception as e:
        print(f"PDF Export error: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Get next access number for ebooks
@app.route('/api/admin/ebooks/next-access-number', methods=['GET'])
@jwt_required()
def get_next_ebook_access_number():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get the highest access number
        last_ebook = Ebook.query.order_by(Ebook.id.desc()).first()

        if not last_ebook or not last_ebook.access_no:
            next_number = "E1"
        else:
            # Try to extract number from access_no
            import re
            numbers = re.findall(r'\d+', last_ebook.access_no)
            if numbers:
                last_number = int(numbers[-1])
                next_number = f"E{last_number + 1}"
            else:
                next_number = "E1"

        return jsonify({'next_access_number': next_number}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Fine Management Routes

# Get all fines
@app.route('/api/admin/fines', methods=['GET'])
@jwt_required()
def get_fines():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        status = request.args.get('status', 'all')  # all, pending, paid
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        query = db.session.query(Fine, User).join(User, Fine.user_id == User.id)

        if status != 'all':
            query = query.filter(Fine.status == status)

        fines = query.order_by(Fine.created_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'fines': [{
                'id': fine.id,
                'user_id': user.user_id,
                'user_name': user.name,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine, user in fines.items],
            'pagination': {
                'page': fines.page,
                'pages': fines.pages,
                'per_page': fines.per_page,
                'total': fines.total
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Add manual fine
@app.route('/api/admin/fines', methods=['POST'])
@jwt_required()
def add_fine():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        amount = data.get('amount')
        reason = data.get('reason')

        if not all([user_id, amount, reason]):
            return jsonify({'error': 'User ID, amount, and reason are required'}), 400

        # Find user
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        fine = Fine(
            user_id=user.id,
            amount=float(amount),
            reason=reason,
            status='pending',
            created_by=current_user_id
        )

        db.session.add(fine)
        db.session.commit()

        return jsonify({
            'message': 'Fine added successfully',
            'fine': {
                'id': fine.id,
                'user_name': user.name,
                'amount': fine.amount,
                'reason': fine.reason
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Mark fine as paid
@app.route('/api/admin/fines/<int:fine_id>/pay', methods=['POST'])
@jwt_required()
def pay_fine(fine_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        fine = Fine.query.get(fine_id)
        if not fine:
            return jsonify({'error': 'Fine not found'}), 404

        if fine.status == 'paid':
            return jsonify({'error': 'Fine already paid'}), 400

        fine.status = 'paid'
        fine.paid_date = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'message': 'Fine marked as paid successfully',
            'fine': {
                'id': fine.id,
                'amount': fine.amount,
                'paid_date': fine.paid_date.isoformat()
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get user's fines (for student dashboard)
@app.route('/api/student/fines', methods=['GET'])
@jwt_required()
def get_user_fines():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        fines = Fine.query.filter_by(user_id=user_id).order_by(Fine.created_date.desc()).all()

        total_pending = sum(fine.amount for fine in fines if fine.status == 'pending')

        return jsonify({
            'fines': [{
                'id': fine.id,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine in fines],
            'total_pending': total_pending
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get fines for a specific user (for payment management)
@app.route('/api/admin/fines/user/<user_id>', methods=['GET'])
@jwt_required()
def get_user_fines_by_id(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Find user by user_id (roll number) or database id
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get all fines for this user
        fines = Fine.query.filter_by(user_id=user.id).order_by(Fine.created_date.desc()).all()

        # Calculate total pending fines
        total_pending = sum(fine.amount for fine in fines if fine.status == 'pending')

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'fines': [{
                'id': fine.id,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine in fines],
            'total_pending': total_pending
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Settings Management Routes

# Get system settings
@app.route('/api/admin/settings', methods=['GET'])
@jwt_required()
def get_settings():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin']:
            return jsonify({'error': 'Admin access required'}), 403

        # Default settings (in a real app, these would be stored in database)
        default_settings = {
            'max_books_per_student': 3,
            'max_books_per_staff': 5,
            'loan_period_days': 14,
            'daily_fine_rate': 1.0,
            'max_renewal_count': 2,
            'renewal_period_days': 7,
            'overdue_grace_period': 0
        }

        return jsonify({'settings': default_settings}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update system settings
@app.route('/api/admin/settings', methods=['POST'])
@jwt_required()
def update_settings():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin']:
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        settings = data.get('settings', {})

        # In a real application, you would save these to a database
        # For now, we'll just validate and return success
        required_settings = [
            'max_books_per_student', 'max_books_per_staff', 'loan_period_days', 'daily_fine_rate',
            'max_renewal_count', 'renewal_period_days', 'overdue_grace_period'
        ]

        for setting in required_settings:
            if setting not in settings:
                return jsonify({'error': f'Missing setting: {setting}'}), 400

        return jsonify({'message': 'Settings updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Gate Entry Management Routes

# Get all gate entry credentials
@app.route('/api/admin/gate-credentials', methods=['GET'])
@jwt_required()
def get_gate_credentials():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credentials = GateEntryCredential.query.all()

        return jsonify({
            'credentials': [{
                'id': cred.id,
                'username': cred.username,
                'name': cred.name,
                'is_active': cred.is_active,
                'created_date': cred.created_date.isoformat(),
                'created_by': cred.created_by_user.name if cred.created_by_user else 'Unknown'
            } for cred in credentials]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Create gate entry credential
@app.route('/api/admin/gate-credentials', methods=['POST'])
@jwt_required()
def create_gate_credential():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        name = data.get('name')

        if not all([username, password, name]):
            return jsonify({'error': 'Username, password, and name are required'}), 400

        # Check if username already exists
        existing = GateEntryCredential.query.filter_by(username=username).first()
        if existing:
            return jsonify({'error': 'Username already exists'}), 400

        credential = GateEntryCredential(
            username=username,
            name=name,
            created_by=current_user_id
        )
        credential.set_password(password)

        db.session.add(credential)
        db.session.commit()

        return jsonify({
            'message': 'Gate entry credential created successfully',
            'credential': {
                'id': credential.id,
                'username': credential.username,
                'name': credential.name
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update gate entry credential
@app.route('/api/admin/gate-credentials/<int:credential_id>', methods=['PUT'])
@jwt_required()
def update_gate_credential(credential_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credential = GateEntryCredential.query.get(credential_id)
        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        data = request.get_json()

        if 'name' in data:
            credential.name = data['name']

        if 'is_active' in data:
            credential.is_active = data['is_active']

        if 'password' in data and data['password']:
            credential.set_password(data['password'])

        db.session.commit()

        return jsonify({'message': 'Credential updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete gate entry credential
@app.route('/api/admin/gate-credentials/<int:credential_id>', methods=['DELETE'])
@jwt_required()
def delete_gate_credential(credential_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credential = GateEntryCredential.query.get(credential_id)
        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        db.session.delete(credential)
        db.session.commit()

        return jsonify({'message': 'Credential deleted successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Gate Entry Dashboard Routes

# Gate entry login
@app.route('/api/gate/login', methods=['POST'])
def gate_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        print(f"Gate login attempt: username={username}")

        if not username or not password:
            return jsonify({'error': 'Username and password are required'}), 400

        credential = GateEntryCredential.query.filter_by(username=username, is_active=True).first()
        print(f"Found credential: {credential}")

        if not credential:
            return jsonify({'error': 'Invalid username'}), 401

        if not credential.check_password(password):
            return jsonify({'error': 'Invalid password'}), 401

        # Create JWT token for gate entry
        access_token = create_access_token(identity=str(credential.id), additional_claims={'type': 'gate'})

        return jsonify({
            'access_token': access_token,
            'credential': {
                'id': credential.id,
                'username': credential.username,
                'name': credential.name
            }
        }), 200

    except Exception as e:
        print(f"Gate login error: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Create default gate entry credential (for testing)
@app.route('/api/gate/create-default', methods=['POST'])
def create_default_gate_credential():
    try:
        # Check if default credential already exists
        existing = GateEntryCredential.query.filter_by(username='gate_operator').first()
        if existing:
            return jsonify({
                'message': 'Default credential already exists',
                'username': 'gate_operator',
                'password': 'password123'
            }), 200

        # Find an admin user to assign as creator
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            # Create a default admin if none exists
            admin_user = User.query.first()

        if not admin_user:
            return jsonify({'error': 'No users found in database'}), 400

        # Create default credential
        credential = GateEntryCredential(
            username='gate_operator',
            name='Default Gate Operator',
            created_by=admin_user.id
        )
        credential.set_password('password123')

        db.session.add(credential)
        db.session.commit()

        return jsonify({
            'message': 'Default gate entry credential created successfully',
            'username': 'gate_operator',
            'password': 'password123'
        }), 201

    except Exception as e:
        print(f"Error creating default credential: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Process barcode scan
@app.route('/api/gate/scan', methods=['POST'])
@jwt_required()
def process_barcode_scan():
    try:
        # Verify this is a gate entry token
        claims = get_jwt()
        if claims.get('type') != 'gate':
            return jsonify({'error': 'Invalid token type'}), 403

        gate_credential_id = int(get_jwt_identity())
        gate_credential = GateEntryCredential.query.get(gate_credential_id)
        if not gate_credential or not gate_credential.is_active:
            return jsonify({'error': 'Invalid gate credential'}), 403

        data = request.get_json()
        barcode = data.get('barcode', '').strip()

        print(f"🔍 Gate scan request - Barcode: '{barcode}'")

        if not barcode:
            print("❌ No barcode provided")
            return jsonify({'error': 'Barcode is required'}), 400

        # Find user by user_id (barcode should match user_id)
        user = User.query.filter_by(user_id=barcode).first()
        print(f"🔍 User lookup for barcode '{barcode}': {'Found' if user else 'Not found'}")

        if not user:
            print(f"❌ User not found for barcode: {barcode}")
            return jsonify({'error': 'User not found. Invalid barcode.'}), 404

        print(f"✅ Found user: {user.name} (ID: {user.user_id})")

        # Check if user has an active entry (last log with status 'in' and no exit_time)
        last_log = GateEntryLog.query.filter_by(user_id=user.id).order_by(GateEntryLog.created_date.desc()).first()

        current_time = datetime.utcnow()
        log_entry = None

        if last_log and last_log.status == 'in' and not last_log.exit_time:
            # User is exiting - record exit time
            last_log.exit_time = current_time
            last_log.status = 'out'
            action = 'exit'
            message = f'Exit recorded for {user.name} at {current_time.strftime("%H:%M:%S")}'
            log_entry = last_log
            print(f"🚪 Exit recorded for {user.name}")
        else:
            # User is entering - create new entry log
            new_log = GateEntryLog(
                user_id=user.id,
                entry_time=current_time,
                status='in',
                scanned_by=gate_credential_id
            )
            db.session.add(new_log)
            action = 'entry'
            message = f'Entry recorded for {user.name} at {current_time.strftime("%H:%M:%S")}'
            log_entry = new_log
            print(f"🚪 Entry recorded for {user.name}")

        db.session.commit()

        # Prepare response data
        response_data = {
            'success': True,
            'action': action,
            'entry_type': action,  # Add entry_type for frontend compatibility
            'message': message,
            'student': {  # Changed from 'user' to 'student' for frontend compatibility
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'user': {  # Keep 'user' for backward compatibility
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'log_entry': {  # Include log entry details for table display
                'id': log_entry.id,
                'user_id': user.user_id,
                'name': user.name,
                'entry_time': log_entry.entry_time.isoformat() if log_entry.entry_time else None,

                'exit_time': log_entry.exit_time.isoformat() if log_entry.exit_time else None,
                'status': log_entry.status,
                'created_date': log_entry.created_date.isoformat()
            },
            'timestamp': current_time.isoformat()
        }

        print(f"✅ Gate scan successful - Action: {action}, User: {user.name}")
        print(f"📤 Sending response: {response_data}")

        return jsonify(response_data), 200

    except Exception as e:
        print(f"❌ Gate scan error: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# Get recent gate entry logs for dashboard
@app.route('/api/gate/recent-logs', methods=['GET'])
@jwt_required()
def get_recent_gate_logs():
    try:
        # Verify this is a gate entry token
        claims = get_jwt()
        if claims.get('type') != 'gate':
            return jsonify({'error': 'Invalid token type'}), 403

        # Get recent logs (last 20 entries)
        recent_logs = db.session.query(GateEntryLog, User).join(
            User, GateEntryLog.user_id == User.id
        ).order_by(GateEntryLog.created_date.desc()).limit(20).all()

        logs_data = []
        for log, user in recent_logs:
            logs_data.append({
                'id': log.id,
                'user_id': user.user_id,
                'name': user.name,
                'entry_time': log.entry_time.isoformat() if log.entry_time else None,
                'exit_time': log.exit_time.isoformat() if log.exit_time else None,
                'status': log.status,
                'created_date': log.created_date.isoformat()
            })

        return jsonify({
            'success': True,
            'logs': logs_data
        }), 200

    except Exception as e:
        print(f"❌ Error fetching recent logs: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Transaction Statistics for Admin
@app.route('/api/admin/transaction-statistics', methods=['GET'])
@jwt_required()
def get_admin_transaction_statistics():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')

        if not start_date or not end_date:
            return jsonify({'error': 'Start date and end date are required'}), 400

        # Base query for transactions (using Circulation model)
        query = db.session.query(Circulation).join(User)

        # Apply date filter
        query = query.filter(Circulation.issue_date >= start_date)
        query = query.filter(Circulation.issue_date <= end_date)

        # Apply college filter if specified
        if college_id:
            query = query.filter(User.college_id == college_id)

        # Apply department filter if specified
        if department_id:
            query = query.filter(User.department_id == department_id)

        # Get all transactions in the date range
        transactions = query.all()

        # Calculate statistics
        issued_books = len(transactions)
        returned_books = len([t for t in transactions if t.return_date is not None])
        outstanding_books = issued_books - returned_books

        # Get detailed statistics by department
        detailed_stats = []
        if department_id:
            # Single department stats
            dept = Department.query.get(department_id)
            if dept:
                dept_transactions = transactions
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned
                return_rate = round((dept_returned / dept_issued * 100) if dept_issued > 0 else 0, 1)

                detailed_stats.append({
                    'college_name': dept.college.name if dept.college else 'Unknown',
                    'department_name': dept.name,
                    'issued_books': dept_issued,
                    'returned_books': dept_returned,
                    'outstanding_books': dept_outstanding,
                    'return_rate': return_rate
                })
        else:
            # All departments stats (filtered by college if specified)
            departments_query = Department.query
            if college_id:
                departments_query = departments_query.filter(Department.college_id == college_id)
            departments = departments_query.all()

            for dept in departments:
                dept_query = db.session.query(Circulation).join(User).filter(
                    User.department_id == dept.id,
                    Circulation.issue_date >= start_date,
                    Circulation.issue_date <= end_date
                )
                # Add college filter if specified
                if college_id:
                    dept_query = dept_query.filter(User.college_id == college_id)

                dept_transactions = dept_query.all()
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned
                return_rate = round((dept_returned / dept_issued * 100) if dept_issued > 0 else 0, 1)

                if dept_issued > 0:  # Only include departments with transactions
                    detailed_stats.append({
                        'college_name': dept.college.name if dept.college else 'Unknown',
                        'department_name': dept.name,
                        'issued_books': dept_issued,
                        'returned_books': dept_returned,
                        'outstanding_books': dept_outstanding,
                        'return_rate': return_rate
                    })

        # Sort detailed stats by college name, then by department name
        detailed_stats.sort(key=lambda x: (x['college_name'], x['department_name']))

        return jsonify({
            'success': True,
            'issued_books': issued_books,
            'returned_books': returned_books,
            'outstanding_books': outstanding_books,
            'detailed_stats': detailed_stats,
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Transaction Statistics for Librarian
@app.route('/api/librarian/transaction-statistics', methods=['GET'])
@jwt_required()
def get_librarian_transaction_statistics():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'librarian':
            return jsonify({'error': 'Librarian access required'}), 403

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')

        if not start_date or not end_date:
            return jsonify({'error': 'Start date and end date are required'}), 400

        # Base query for transactions (using Circulation model)
        query = db.session.query(Circulation).join(User)

        # Apply date filter
        query = query.filter(Circulation.issue_date >= start_date)
        query = query.filter(Circulation.issue_date <= end_date)

        # Apply college filter if specified
        if college_id:
            query = query.filter(User.college_id == college_id)

        # Apply department filter if specified
        if department_id:
            query = query.filter(User.department_id == department_id)

        # Get all transactions in the date range
        transactions = query.all()

        # Calculate statistics
        issued_books = len(transactions)
        returned_books = len([t for t in transactions if t.return_date is not None])
        outstanding_books = issued_books - returned_books

        # Get detailed statistics by department
        detailed_stats = []
        if department_id:
            # Single department stats
            dept = Department.query.get(department_id)
            if dept:
                dept_transactions = transactions
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned
                return_rate = round((dept_returned / dept_issued * 100) if dept_issued > 0 else 0, 1)

                detailed_stats.append({
                    'college_name': dept.college.name if dept.college else 'Unknown',
                    'department_name': dept.name,
                    'issued_books': dept_issued,
                    'returned_books': dept_returned,
                    'outstanding_books': dept_outstanding,
                    'return_rate': return_rate
                })
        else:
            # All departments stats
            departments = Department.query.all()
            for dept in departments:
                dept_query = db.session.query(Circulation).join(User).filter(
                    User.department_id == dept.id,
                    Circulation.issue_date >= start_date,
                    Circulation.issue_date <= end_date
                )
                dept_transactions = dept_query.all()
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned
                return_rate = round((dept_returned / dept_issued * 100) if dept_issued > 0 else 0, 1)

                if dept_issued > 0:  # Only include departments with transactions
                    detailed_stats.append({
                        'college_name': dept.college.name if dept.college else 'Unknown',
                        'department_name': dept.name,
                        'issued_books': dept_issued,
                        'returned_books': dept_returned,
                        'outstanding_books': dept_outstanding,
                        'return_rate': return_rate
                    })

        # Sort detailed stats by college name, then by department name
        detailed_stats.sort(key=lambda x: (x['college_name'], x['department_name']))

        return jsonify({
            'success': True,
            'issued_books': issued_books,
            'returned_books': returned_books,
            'outstanding_books': outstanding_books,
            'detailed_stats': detailed_stats,
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Download Transaction Statistics PDF for Admin
@app.route('/api/admin/transaction-statistics/download', methods=['GET'])
@jwt_required()
def download_admin_transaction_statistics():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        department_id = request.args.get('department_id')

        if not start_date or not end_date:
            return jsonify({'error': 'Start date and end date are required'}), 400

        # Get statistics data (reuse the logic from the main endpoint)
        query = db.session.query(Circulation).join(User)
        query = query.filter(Circulation.issue_date >= start_date)
        query = query.filter(Circulation.issue_date <= end_date)

        if department_id:
            query = query.filter(User.department_id == department_id)

        transactions = query.all()
        issued_books = len(transactions)
        returned_books = len([t for t in transactions if t.return_date is not None])
        outstanding_books = issued_books - returned_books

        # Create a simple text-based report (you can enhance this with actual PDF generation)
        report_content = f"""
TRANSACTION STATISTICS REPORT
============================

Date Range: {start_date} to {end_date}
Generated on: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY:
--------
Books Issued: {issued_books}
Books Returned: {returned_books}
Outstanding Books: {outstanding_books}
Return Rate: {round((returned_books / issued_books * 100) if issued_books > 0 else 0, 1)}%

DETAILED BREAKDOWN:
------------------
"""

        # Add department-wise breakdown
        if department_id:
            dept = Department.query.get(department_id)
            if dept:
                college_name = dept.college.name if dept.college else 'Unknown'
                report_content += f"College: {college_name}\nDepartment: {dept.name}\n"
        else:
            departments = Department.query.all()
            # Sort departments by college name, then department name
            departments.sort(key=lambda d: (d.college.name if d.college else 'Unknown', d.name))

            for dept in departments:
                dept_query = db.session.query(Circulation).join(User).filter(
                    User.department_id == dept.id,
                    Circulation.issue_date >= start_date,
                    Circulation.issue_date <= end_date
                )
                dept_transactions = dept_query.all()
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned

                if dept_issued > 0:
                    college_name = dept.college.name if dept.college else 'Unknown'
                    report_content += f"{college_name} - {dept.name}: Issued={dept_issued}, Returned={dept_returned}, Outstanding={dept_outstanding}\n"

        # Return as downloadable text file (you can enhance this to actual PDF)
        from flask import make_response
        response = make_response(report_content)
        response.headers['Content-Type'] = 'text/plain'
        response.headers['Content-Disposition'] = f'attachment; filename=transaction_statistics_{start_date}_to_{end_date}.txt'

        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Download Transaction Statistics PDF for Librarian
@app.route('/api/librarian/transaction-statistics/download', methods=['GET'])
@jwt_required()
def download_librarian_transaction_statistics():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'librarian':
            return jsonify({'error': 'Librarian access required'}), 403

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        department_id = request.args.get('department_id')

        if not start_date or not end_date:
            return jsonify({'error': 'Start date and end date are required'}), 400

        # Get statistics data (reuse the logic from the main endpoint)
        query = db.session.query(Circulation).join(User)
        query = query.filter(Circulation.issue_date >= start_date)
        query = query.filter(Circulation.issue_date <= end_date)

        if department_id:
            query = query.filter(User.department_id == department_id)

        transactions = query.all()
        issued_books = len(transactions)
        returned_books = len([t for t in transactions if t.return_date is not None])
        outstanding_books = issued_books - returned_books

        # Create a simple text-based report
        report_content = f"""
TRANSACTION STATISTICS REPORT
============================

Date Range: {start_date} to {end_date}
Generated on: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY:
--------
Books Issued: {issued_books}
Books Returned: {returned_books}
Outstanding Books: {outstanding_books}
Return Rate: {round((returned_books / issued_books * 100) if issued_books > 0 else 0, 1)}%

DETAILED BREAKDOWN:
------------------
"""

        # Add department-wise breakdown
        if department_id:
            dept = Department.query.get(department_id)
            if dept:
                college_name = dept.college.name if dept.college else 'Unknown'
                report_content += f"College: {college_name}\nDepartment: {dept.name}\n"
        else:
            departments = Department.query.all()
            # Sort departments by college name, then department name
            departments.sort(key=lambda d: (d.college.name if d.college else 'Unknown', d.name))

            for dept in departments:
                dept_query = db.session.query(Circulation).join(User).filter(
                    User.department_id == dept.id,
                    Circulation.issue_date >= start_date,
                    Circulation.issue_date <= end_date
                )
                dept_transactions = dept_query.all()
                dept_issued = len(dept_transactions)
                dept_returned = len([t for t in dept_transactions if t.return_date is not None])
                dept_outstanding = dept_issued - dept_returned

                if dept_issued > 0:
                    college_name = dept.college.name if dept.college else 'Unknown'
                    report_content += f"{college_name} - {dept.name}: Issued={dept_issued}, Returned={dept_returned}, Outstanding={dept_outstanding}\n"

        # Return as downloadable text file
        from flask import make_response
        response = make_response(report_content)
        response.headers['Content-Type'] = 'text/plain'
        response.headers['Content-Disposition'] = f'attachment; filename=transaction_statistics_{start_date}_to_{end_date}.txt'

        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500





# Get colleges for librarian
@app.route('/api/librarian/colleges', methods=['GET'])
@jwt_required()
def get_librarian_colleges():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'librarian':
            return jsonify({'error': 'Librarian access required'}), 403

        colleges = College.query.all()
        colleges_data = [{'id': college.id, 'name': college.name} for college in colleges]

        return jsonify({
            'success': True,
            'colleges': colleges_data
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get departments for librarian
@app.route('/api/librarian/departments', methods=['GET'])
@jwt_required()
def get_librarian_departments():
    try:
        current_user = get_jwt_identity()
        user = User.query.get(current_user)

        if not user or user.role != 'librarian':
            return jsonify({'error': 'Librarian access required'}), 403

        college_id = request.args.get('college_id')

        departments_query = Department.query
        if college_id:
            departments_query = departments_query.filter(Department.college_id == college_id)

        departments = departments_query.all()
        departments_data = [{
            'id': dept.id,
            'name': dept.name,
            'college_id': dept.college_id,
            'college_name': dept.college.name if dept.college else 'Unknown'
        } for dept in departments]

        return jsonify({
            'success': True,
            'departments': departments_data
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get gate entry logs
@app.route('/api/admin/gate-logs', methods=['GET'])
@jwt_required()
def get_gate_logs():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        status_filter = request.args.get('status')
        export_format = request.args.get('format')

        query = db.session.query(GateEntryLog, User, College, Department).join(
            User, GateEntryLog.user_id == User.id
        ).outerjoin(
            College, User.college_id == College.id
        ).outerjoin(
            Department, User.department_id == Department.id
        )

        # Apply filters
        if from_date:
            from datetime import datetime
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
            query = query.filter(GateEntryLog.created_date >= from_date_obj)

        if to_date:
            from datetime import datetime
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(GateEntryLog.created_date <= to_date_obj)

        if college_id and college_id != 'all':
            query = query.filter(User.college_id == int(college_id))

        if department_id and department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        if status_filter and status_filter != 'all':
            query = query.filter(GateEntryLog.status == status_filter)

        if export_format:
            # For export, get all results without pagination
            results = query.order_by(GateEntryLog.created_date.desc()).all()

            report_data = []
            for log, user, college, department in results:
                report_data.append({
                    'user_id': user.user_id,
                    'user_name': user.name,
                    'college': college.name if college else 'N/A',
                    'department': department.name if department else 'N/A',
                    'entry_time': log.entry_time.strftime('%Y-%m-%d %H:%M:%S') if log.entry_time else 'N/A',
                    'exit_time': log.exit_time.strftime('%Y-%m-%d %H:%M:%S') if log.exit_time else 'N/A',
                    'status': 'Inside' if log.status == 'in' else 'Exited',
                    'created_date': log.created_date.strftime('%Y-%m-%d')
                })

            if export_format == 'excel':
                return generate_excel_report(report_data, 'Gate_Entry_Report')
            elif export_format == 'pdf':
                return generate_pdf_report(report_data, 'Gate Entry Report', [
                    'Student ID', 'Name', 'College', 'Department', 'Entry Time',
                    'Exit Time', 'Status', 'Date'
                ])
        else:
            # Regular paginated response
            logs = query.order_by(GateEntryLog.created_date.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

            return jsonify({
                'logs': [{
                    'id': log.id,
                    'user_id': user.user_id,
                    'user_name': user.name,
                    'college': college.name if college else None,
                    'department': department.name if department else None,
                    'entry_time': log.entry_time.isoformat() if log.entry_time else None,
                    'exit_time': log.exit_time.isoformat() if log.exit_time else None,
                    'status': log.status,
                    'created_date': log.created_date.isoformat()
                } for log, user, college, department in logs.items],
                'pagination': {
                    'page': logs.page,
                    'pages': logs.pages,
                    'per_page': logs.per_page,
                    'total': logs.total
                }
            }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Dashboard Stats API
@app.route('/api/admin/dashboard-stats', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get total books
        total_books = Book.query.count()

        # Get total students and librarians
        total_students = User.query.filter_by(role='student').count()
        total_librarians = User.query.filter_by(role='librarian').count()

        # Get total colleges
        total_colleges = College.query.count()

        # Get active circulations (issued books)
        active_circulations = Circulation.query.filter_by(status='issued').count()

        # Get total available books
        available_books = db.session.query(db.func.sum(Book.available_copies)).scalar() or 0

        # Get total fines
        total_fines = db.session.query(db.func.sum(Fine.amount)).filter_by(status='pending').scalar() or 0

        # Get overdue books
        overdue_books = Circulation.query.filter(
            Circulation.status == 'issued',
            Circulation.due_date < datetime.utcnow()
        ).count()

        return jsonify({
            'totalBooks': total_books,
            'totalEbooks': 5,  # Mock data for e-books
            'totalStudents': total_students,
            'totalLibrarians': total_librarians,
            'totalColleges': total_colleges,
            'activeCirculations': active_circulations,
            'availableBooks': available_books,
            'totalFines': total_fines,
            'overdueBooks': overdue_books
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Report Generation Helper Functions
def generate_excel_report(data, filename):
    """Generate Excel report from data"""
    try:
        import pandas as pd
        import io
        from flask import send_file

        df = pd.DataFrame(data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Report', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{filename}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
    except Exception as e:
        return jsonify({'error': f'Failed to generate Excel report: {str(e)}'}), 500

def generate_pdf_report(data, title, columns):
    """Generate PDF report from data"""
    try:
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        import io
        from flask import send_file

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        elements = []

        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        # Add title
        elements.append(Paragraph(title, title_style))
        elements.append(Spacer(1, 12))

        # Prepare table data
        table_data = [columns]  # Header row
        for row in data:
            table_data.append([str(row.get(col.lower().replace(' ', '_'), '')) for col in columns])

        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)
        doc.build(elements)

        buffer.seek(0)

        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'{title.replace(" ", "_")}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )
    except Exception as e:
        return jsonify({'error': f'Failed to generate PDF report: {str(e)}'}), 500

# Reporting System Routes

# Fine Reports
@app.route('/api/admin/reports/fines', methods=['GET'])
@jwt_required()
def get_fine_reports():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get query parameters
        status_filter = request.args.get('status', 'all')  # all, paid, unpaid
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        export_format = request.args.get('format')  # pdf, excel

        # Build query
        query = db.session.query(Fine, User, College, Department).join(
            User, Fine.user_id == User.id
        ).outerjoin(
            College, User.college_id == College.id
        ).outerjoin(
            Department, User.department_id == Department.id
        )

        # Apply filters
        if status_filter == 'paid':
            query = query.filter(Fine.status == 'paid')
        elif status_filter == 'unpaid':
            query = query.filter(Fine.status == 'pending')

        if from_date:
            from datetime import datetime
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
            query = query.filter(Fine.created_date >= from_date_obj)

        if to_date:
            from datetime import datetime
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            # Add one day to include the entire to_date
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Fine.created_date <= to_date_obj)

        if college_id and college_id != 'all':
            query = query.filter(User.college_id == int(college_id))

        if department_id and department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        results = query.order_by(Fine.created_date.desc()).all()

        # Format data
        report_data = []
        for fine, user, college, department in results:
            report_data.append({
                'student_id': user.user_id,
                'name': user.name,
                'college': college.name if college else 'N/A',
                'department': department.name if department else 'N/A',
                'fine_amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.strftime('%Y-%m-%d %H:%M:%S'),
                'paid_date': fine.paid_date.strftime('%Y-%m-%d %H:%M:%S') if fine.paid_date else 'N/A'
            })

        # Handle export formats
        if export_format == 'excel':
            return generate_excel_report(report_data, 'Fine_Report')
        elif export_format == 'pdf':
            return generate_pdf_report(report_data, 'Fine Report', [
                'Student ID', 'Name', 'College', 'Department', 'Fine Amount',
                'Reason', 'Status', 'Created Date', 'Paid Date'
            ])

        return jsonify({
            'data': report_data,
            'total': len(report_data)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Counter Reports
@app.route('/api/admin/reports/counter', methods=['GET'])
@jwt_required()
def get_counter_reports():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get query parameters
        report_type = request.args.get('type', 'issue')  # issue, return
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        export_format = request.args.get('format')  # pdf, excel

        # Build query
        query = db.session.query(Circulation, User, Book, College, Department).join(
            User, Circulation.user_id == User.id
        ).join(
            Book, Circulation.book_id == Book.id
        ).outerjoin(
            College, User.college_id == College.id
        ).outerjoin(
            Department, User.department_id == Department.id
        )

        # Apply report type filter
        if report_type == 'return':
            query = query.filter(Circulation.status == 'returned')
        else:  # issue
            query = query.filter(Circulation.status.in_(['issued', 'returned']))

        # Apply date filters
        if from_date:
            from datetime import datetime
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
            if report_type == 'return':
                query = query.filter(Circulation.return_date >= from_date_obj)
            else:
                query = query.filter(Circulation.issue_date >= from_date_obj)

        if to_date:
            from datetime import datetime
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)
            if report_type == 'return':
                query = query.filter(Circulation.return_date <= to_date_obj)
            else:
                query = query.filter(Circulation.issue_date <= to_date_obj)

        if college_id and college_id != 'all':
            query = query.filter(User.college_id == int(college_id))

        if department_id and department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        results = query.order_by(Circulation.issue_date.desc()).all()

        # Format data
        report_data = []
        for circulation, user, book, college, department in results:
            if report_type == 'return':
                report_data.append({
                    'student_id': user.user_id,
                    'name': user.name,
                    'college': college.name if college else 'N/A',
                    'department': department.name if department else 'N/A',
                    'book_title': book.title,
                    'author': book.author,
                    'access_no': book.access_no,
                    'issue_date': circulation.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'due_date': circulation.due_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'return_date': circulation.return_date.strftime('%Y-%m-%d %H:%M:%S') if circulation.return_date else 'N/A',
                    'fine_amount': circulation.fine_amount or 0
                })
            else:  # issue
                report_data.append({
                    'student_id': user.user_id,
                    'name': user.name,
                    'college': college.name if college else 'N/A',
                    'department': department.name if department else 'N/A',
                    'book_title': book.title,
                    'author': book.author,
                    'access_no': book.access_no,
                    'issue_date': circulation.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'due_date': circulation.due_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'status': circulation.status
                })

        # Handle export formats
        if export_format == 'excel':
            return generate_excel_report(report_data, f'{report_type.title()}_Report')
        elif export_format == 'pdf':
            if report_type == 'return':
                columns = ['Student ID', 'Name', 'College', 'Department', 'Book Title',
                          'Author', 'Access No', 'Issue Date', 'Due Date', 'Return Date', 'Fine Amount']
            else:
                columns = ['Student ID', 'Name', 'College', 'Department', 'Book Title',
                          'Author', 'Access No', 'Issue Date', 'Due Date', 'Status']
            return generate_pdf_report(report_data, f'{report_type.title()} Report', columns)

        return jsonify({
            'data': report_data,
            'total': len(report_data)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/student/dashboard', methods=['GET'])
@jwt_required()
def get_student_dashboard():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get current borrowed books
        current_books = db.session.query(Circulation, Book).join(
            Book, Circulation.book_id == Book.id
        ).filter(
            Circulation.user_id == user_id,
            Circulation.status == 'issued'
        ).all()

        # Get user's reservations
        reservations = db.session.query(Reservation, Book).join(
            Book, Reservation.book_id == Book.id
        ).filter(
            Reservation.user_id == user_id,
            Reservation.status == 'active'
        ).all()

        # Get total books read (returned books)
        total_books_read = Circulation.query.filter_by(
            user_id=user_id,
            status='returned'
        ).count()

        # Get total pending fines
        total_fines = db.session.query(db.func.sum(Fine.amount)).filter_by(
            user_id=user_id,
            status='pending'
        ).scalar() or 0

        # Format borrowed books
        borrowed_books = []
        for circulation, book in current_books:
            # Calculate days remaining
            from datetime import date
            days_remaining = (circulation.due_date.date() - date.today()).days
            is_overdue = days_remaining < 0

            borrowed_books.append({
                'id': circulation.id,
                'book_id': book.id,
                'book_title': book.title,
                'book_author': book.author_1 or book.author,
                'access_no': book.access_no,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'days_remaining': days_remaining,
                'is_overdue': is_overdue,
                'renewal_count': circulation.renewal_count or 0,
                'max_renewals': circulation.max_renewals or 2,
                'can_renew': circulation.can_renew()[0] if hasattr(circulation, 'can_renew') else False
            })

        # Format reservations
        reservations_data = []
        for reservation, book in reservations:
            estimated_date = reservation.calculate_estimated_availability()
            reservations_data.append({
                'id': reservation.id,
                'book_id': book.id,
                'title': book.title,
                'author': book.author_1 or book.author,
                'access_no': book.access_no,
                'reservation_date': reservation.reservation_date.isoformat(),
                'queue_position': reservation.queue_position,
                'estimated_availability': estimated_date.isoformat(),
                'expiry_date': reservation.expiry_date.isoformat()
            })

        return jsonify({
            'user': {
                'name': user.name,
                'user_id': user.user_id,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None
            },
            'stats': {
                'books_borrowed': len(current_books),
                'books_reserved': len(reservations),
                'total_books_read': total_books_read,
                'total_fines': float(total_fines)
            },
            'borrowed_books': borrowed_books,
            'reservations': reservations_data
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Student Book Reservation
@app.route('/api/student/books/<int:book_id>/reserve', methods=['POST', 'OPTIONS'])
@jwt_required()
def reserve_book(book_id):
    if request.method == 'OPTIONS':
        # Handle preflight request
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        from datetime import datetime, timedelta

        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        # Check if book exists
        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if user already has this book borrowed
        existing_circulation = Circulation.query.filter_by(
            user_id=user_id,
            book_id=book_id,
            status='issued'
        ).first()

        if existing_circulation:
            return jsonify({'error': 'You already have this book borrowed'}), 400

        # Check if user already has this book reserved
        existing_reservation = Reservation.query.filter_by(
            user_id=user_id,
            book_id=book_id,
            status='active'
        ).first()

        if existing_reservation:
            return jsonify({'error': 'You have already reserved this book'}), 400

        # Check if book is available
        if book.available_copies > 0:
            return jsonify({'error': 'Book is available for immediate borrowing. Please contact the librarian.'}), 400

        # Check when the book will be available based on current circulations
        current_circulations = Circulation.query.filter(
            Circulation.book_id == book_id,
            Circulation.status == 'issued'
        ).order_by(Circulation.due_date.asc()).all()

        earliest_return_date = None
        if current_circulations:
            # Find the earliest expected return date
            earliest_return_date = current_circulations[0].due_date

            # Reservation will be available from the day after the earliest return
            available_from = earliest_return_date + timedelta(days=1)

            # Store this information for later use
            book_available_from = available_from
        else:
            # If no current circulations, book should be available now (but we already checked available_copies)
            book_available_from = datetime.utcnow()

        # Get current queue position
        queue_position = Reservation.query.filter_by(
            book_id=book_id,
            status='active'
        ).count() + 1

        # Get pickup date from request data
        data = request.get_json() or {}
        pickup_date_str = data.get('pickup_date')
        notes = data.get('notes', '')

        # Parse pickup date
        pickup_date = None
        if pickup_date_str:
            try:
                pickup_date = datetime.fromisoformat(pickup_date_str.replace('Z', '+00:00'))
            except ValueError:
                try:
                    pickup_date = datetime.strptime(pickup_date_str, '%Y-%m-%d')
                except ValueError:
                    return jsonify({'error': 'Invalid pickup date format. Use YYYY-MM-DD'}), 400

        # Validate pickup date (should be in the future and after book becomes available)
        if pickup_date and pickup_date.date() < datetime.utcnow().date():
            return jsonify({'error': 'Pickup date must be today or in the future'}), 400

        # Validate pickup date against book availability
        if pickup_date and earliest_return_date:
            available_from = earliest_return_date + timedelta(days=1)
            if pickup_date.date() < available_from.date():
                return jsonify({
                    'error': f'This book is expected to be returned on {earliest_return_date.strftime("%B %d, %Y")}. Reservations are available from {available_from.strftime("%B %d, %Y")} onwards. Please select a pickup date from {available_from.strftime("%B %d, %Y")} or later.',
                    'earliest_available_date': available_from.strftime('%Y-%m-%d'),
                    'expected_return_date': earliest_return_date.strftime('%Y-%m-%d')
                }), 400

        # Create reservation with pickup date and expiry date (30 days from now)

        reservation = Reservation(
            user_id=user_id,
            book_id=book_id,
            queue_position=queue_position,
            status='active',
            reservation_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=30),
            pickup_deadline=pickup_date,
            notes=notes
        )

        db.session.add(reservation)
        db.session.commit()

        return jsonify({
            'message': 'Book reserved successfully',
            'queue_position': queue_position,
            'pickup_date': pickup_date.isoformat() if pickup_date else None,
            'reservation_id': reservation.id,
            'expiry_date': reservation.expiry_date.isoformat()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/student/reservations', methods=['GET'])
@jwt_required()
def get_student_reservations():
    """Get all reservations for the current student"""
    try:
        current_user_id = get_jwt_identity()

        # Get user information
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get all reservations for this user with book details
        reservations = db.session.query(Reservation, Book).join(
            Book, Reservation.book_id == Book.id
        ).filter(
            Reservation.user_id == current_user_id
        ).order_by(Reservation.reservation_date.desc()).all()

        reservation_list = []
        for reservation, book in reservations:
            reservation_data = {
                'id': reservation.id,
                'queue_position': reservation.queue_position,
                'status': reservation.status,
                'reservation_date': reservation.reservation_date.isoformat(),
                'pickup_deadline': reservation.pickup_deadline.isoformat() if reservation.pickup_deadline else None,
                'expiry_date': reservation.expiry_date.isoformat() if reservation.expiry_date else None,
                'notes': reservation.notes,
                'book': {
                    'id': book.id,
                    'title': book.title,
                    'author_1': book.author_1,
                    'author': book.author_1,  # For backward compatibility
                    'access_no': book.access_no,
                    'isbn': book.isbn,
                    'category': book.category,
                    'available_copies': book.available_copies,
                    'total_copies': book.number_of_copies
                }
            }
            reservation_list.append(reservation_data)

        return jsonify({
            'reservations': reservation_list,
            'total': len(reservation_list)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/student/reservations/<int:reservation_id>/cancel', methods=['DELETE'])
@jwt_required()
def cancel_student_reservation(reservation_id):
    """Cancel a student's reservation"""
    try:
        current_user_id = get_jwt_identity()

        # Get the reservation
        reservation = Reservation.query.filter_by(
            id=reservation_id,
            user_id=current_user_id
        ).first()

        if not reservation:
            return jsonify({'error': 'Reservation not found'}), 404

        if reservation.status != 'active':
            return jsonify({'error': 'Only active reservations can be cancelled'}), 400

        # Update reservation status
        reservation.status = 'cancelled'

        # Update queue positions for other reservations of the same book
        other_reservations = Reservation.query.filter(
            Reservation.book_id == reservation.book_id,
            Reservation.status == 'active',
            Reservation.queue_position > reservation.queue_position
        ).all()

        for other_reservation in other_reservations:
            other_reservation.queue_position -= 1

        db.session.commit()

        return jsonify({'message': 'Reservation cancelled successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Student Circulation Renewal
@app.route('/api/student/circulations/<int:circulation_id>/renew', methods=['POST', 'OPTIONS'])
@jwt_required()
def renew_circulation(circulation_id):
    if request.method == 'OPTIONS':
        # Handle preflight request
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        # Get circulation record
        circulation = Circulation.query.get(circulation_id)
        if not circulation:
            return jsonify({'error': 'Circulation record not found'}), 404

        # Check if this circulation belongs to the user
        if circulation.user_id != user_id:
            return jsonify({'error': 'Unauthorized access'}), 403

        # Check if book can be renewed
        can_renew, reason = circulation.can_renew()
        if not can_renew:
            return jsonify({'error': reason}), 400

        # Renew the book (extend due date by 14 days)
        from datetime import datetime, timedelta
        circulation.due_date = datetime.utcnow() + timedelta(days=14)
        circulation.renewal_count = (circulation.renewal_count or 0) + 1

        db.session.commit()

        return jsonify({
            'message': 'Book renewed successfully',
            'new_due_date': circulation.due_date.isoformat()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Check Book Reservation Status
@app.route('/api/books/<access_no>/reservation-status', methods=['GET'])
@jwt_required()
def check_book_reservation_status(access_no):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin or librarian access required'}), 403

        # Find book by access number
        book = Book.query.filter_by(access_no=access_no).first()
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check for active reservations
        active_reservations = db.session.query(Reservation, User).join(
            User, Reservation.user_id == User.id
        ).filter(
            Reservation.book_id == book.id,
            Reservation.status == 'active'
        ).order_by(Reservation.queue_position).all()

        reservation_data = []
        for reservation, reserved_user in active_reservations:
            reservation_data.append({
                'id': reservation.id,
                'student_name': reserved_user.name,
                'student_id': reserved_user.user_id,
                'student_email': reserved_user.email,
                'reservation_date': reservation.reservation_date.isoformat(),
                'expiry_date': reservation.expiry_date.isoformat() if reservation.expiry_date else None,
                'queue_position': reservation.queue_position,
                'notes': reservation.notes
            })

        return jsonify({
            'book_id': book.id,
            'book_title': book.title,
            'book_author': book.author,
            'access_no': book.access_no,
            'available_copies': book.available_copies,
            'has_reservations': len(reservation_data) > 0,
            'total_reservations': len(reservation_data),
            'reservations': reservation_data
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Duplicate removed - using the cleaner implementation above

# Student Books API
@app.route('/api/student/books', methods=['GET'])
@jwt_required()
def get_student_books():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 12))
        search = request.args.get('search', '')
        category = request.args.get('category', 'all')
        availability = request.args.get('availability', 'all')

        query = Book.query

        # Apply search filter
        if search:
            query = query.filter(
                db.or_(
                    Book.title.ilike(f'%{search}%'),
                    Book.author.ilike(f'%{search}%'),
                    Book.isbn.ilike(f'%{search}%')
                )
            )

        # Apply category filter
        if category != 'all':
            query = query.filter(Book.category.ilike(f'%{category}%'))

        # Apply availability filter
        if availability == 'available':
            query = query.filter(Book.available_copies > 0)

        books = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'books': [{
                'id': book.id,
                'title': book.title,
                'author': book.author,
                'isbn': book.isbn,
                'access_no': book.access_no,
                'category': book.category,
                'number_of_copies': book.number_of_copies,
                'available_copies': book.available_copies,
                'created_at': book.created_at.isoformat()
            } for book in books.items],
            'total': books.total,
            'pages': books.pages,
            'current_page': books.page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Student E-books API
@app.route('/api/student/ebooks', methods=['GET'])
@jwt_required()
def get_student_ebooks():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 12))
        search = request.args.get('search', '')
        category = request.args.get('category', 'all')
        format_filter = request.args.get('format', 'all')

        # Mock e-book data for demonstration
        mock_ebooks = [
            {
                'id': 1,
                'title': 'Introduction to Computer Science',
                'author': 'John Smith',
                'description': 'A comprehensive guide to computer science fundamentals',
                'category': 'technology',
                'file_format': 'pdf',
                'file_size': 2048000,
                'download_count': 45,
                'created_at': '2024-01-15T10:00:00Z'
            },
            {
                'id': 2,
                'title': 'Modern Web Development',
                'author': 'Jane Doe',
                'description': 'Learn modern web development techniques and frameworks',
                'category': 'technology',
                'file_format': 'pdf',
                'file_size': 3072000,
                'download_count': 32,
                'created_at': '2024-02-01T14:30:00Z'
            },
            {
                'id': 3,
                'title': 'Data Structures and Algorithms',
                'author': 'Robert Johnson',
                'description': 'Master data structures and algorithms for programming',
                'category': 'science',
                'file_format': 'epub',
                'file_size': 1536000,
                'download_count': 67,
                'created_at': '2024-01-20T09:15:00Z'
            },
            {
                'id': 4,
                'title': 'Digital Marketing Fundamentals',
                'author': 'Sarah Wilson',
                'description': 'Learn the basics of digital marketing and online advertising',
                'category': 'non-fiction',
                'file_format': 'pdf',
                'file_size': 1800000,
                'download_count': 28,
                'created_at': '2024-01-25T16:45:00Z'
            },
            {
                'id': 5,
                'title': 'World History: Ancient Civilizations',
                'author': 'Michael Brown',
                'description': 'Explore the rise and fall of ancient civilizations',
                'category': 'history',
                'file_format': 'epub',
                'file_size': 2500000,
                'download_count': 53,
                'created_at': '2024-02-10T11:20:00Z'
            }
        ]

        # Apply filters
        filtered_ebooks = mock_ebooks
        if search:
            filtered_ebooks = [e for e in filtered_ebooks if
                             search.lower() in e['title'].lower() or
                             search.lower() in e['author'].lower() or
                             search.lower() in e['description'].lower()]

        if category != 'all':
            filtered_ebooks = [e for e in filtered_ebooks if e['category'] == category]

        if format_filter != 'all':
            filtered_ebooks = [e for e in filtered_ebooks if e['file_format'] == format_filter]

        # Pagination
        total = len(filtered_ebooks)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_ebooks = filtered_ebooks[start:end]

        return jsonify({
            'ebooks': paginated_ebooks,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Student Borrowing History API
@app.route('/api/student/borrowing-history', methods=['GET'])
@jwt_required()
def get_student_borrowing_history():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'student':
            return jsonify({'error': 'Student access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        status_filter = request.args.get('status', 'all')

        query = db.session.query(Circulation, Book).join(
            Book, Circulation.book_id == Book.id
        ).filter(Circulation.user_id == current_user_id)

        # Apply status filter
        if status_filter == 'current':
            query = query.filter(Circulation.status == 'issued')
        elif status_filter == 'returned':
            query = query.filter(Circulation.status == 'returned')
        elif status_filter == 'overdue':
            query = query.filter(
                Circulation.status == 'issued',
                Circulation.due_date < datetime.utcnow()
            )

        history = query.order_by(Circulation.issue_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'history': [{
                'id': circulation.id,
                'book_title': book.title,
                'book_author': book.author,
                'access_no': book.access_no,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'return_date': circulation.return_date.isoformat() if circulation.return_date else None,
                'status': circulation.status,
                'fine_amount': circulation.fine_amount or 0,
                'renewal_count': getattr(circulation, 'renewal_count', 0)
            } for circulation, book in history.items],
            'total': history.total,
            'pages': history.pages,
            'current_page': history.page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/students/sample', methods=['GET'])
@jwt_required()
def download_students_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        import pandas as pd
        import io
        from flask import send_file
        from datetime import date, timedelta

        # Create sample data
        sample_data = {
            'user_id': ['CS2024001', 'CS2024002', 'IT2024001'],
            'name': ['John Doe', 'Jane Smith', 'Robert Johnson'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'dob': ['2000-01-15', '1999-12-20', '2001-03-10'],
            'validity_date': [(date.today() + timedelta(days=365)).isoformat(),
                             (date.today() + timedelta(days=365)).isoformat(),
                             (date.today() + timedelta(days=365)).isoformat()]
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Students Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='students_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Ebook Management Routes
@app.route('/api/admin/ebooks', methods=['GET'])
@jwt_required()
def get_ebooks():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        ebook_type = request.args.get('type', '')

        query = Ebook.query
        if search:
            query = query.filter(
                db.or_(
                    Ebook.web_title.ilike(f'%{search}%'),
                    Ebook.subject.ilike(f'%{search}%'),
                    Ebook.access_no.ilike(f'%{search}%'),
                    Ebook.website.ilike(f'%{search}%')
                )
            )

        if ebook_type:
            query = query.filter(Ebook.type.ilike(f'%{ebook_type}%'))

        ebooks = query.order_by(Ebook.created_at.desc()).paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'ebooks': [{
                'id': ebook.id,
                'access_no': ebook.access_no,
                'website': ebook.website,
                'web_detail': ebook.web_detail,
                'web_title': ebook.web_title,
                'subject': ebook.subject,
                'type': ebook.type,
                'download_count': ebook.download_count,
                'created_by': ebook.creator.name if ebook.creator else 'Unknown',
                'created_at': ebook.created_at.isoformat()
            } for ebook in ebooks.items],
            'pagination': {
                'page': ebooks.page,
                'pages': ebooks.pages,
                'per_page': ebooks.per_page,
                'total': ebooks.total,
                'has_next': ebooks.has_next,
                'has_prev': ebooks.has_prev
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks', methods=['POST'])
@jwt_required()
def create_ebook():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()

        # Validate required fields
        required_fields = ['access_no', 'website', 'web_title', 'subject', 'type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        # Check if access number already exists
        existing = Ebook.query.filter_by(access_no=data.get('access_no')).first()
        if existing:
            return jsonify({'error': 'Access number already exists'}), 400

        # Validate type
        valid_types = ['E-journal', 'E-book', 'E-journal Portal', 'E-journal Book', 'Database', 'Others']
        if data['type'] not in valid_types:
            return jsonify({'error': 'Invalid type'}), 400

        ebook = Ebook(
            access_no=data.get('access_no'),
            website=data.get('website'),
            web_detail=data.get('web_detail', ''),
            web_title=data.get('web_title'),
            subject=data.get('subject'),
            type=data.get('type'),
            created_by=user_id
        )

        db.session.add(ebook)
        db.session.commit()

        return jsonify({
            'message': 'E-book created successfully',
            'ebook': {
                'id': ebook.id,
                'access_no': ebook.access_no,
                'web_title': ebook.web_title,
                'subject': ebook.subject,
                'type': ebook.type
            }
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks/sample', methods=['GET'])
@jwt_required()
def download_ebooks_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        import pandas as pd
        import io
        from flask import send_file

        # Create sample data
        sample_data = {
            'access_no': ['E001', 'E002', 'E003'],
            'title': ['Digital Signal Processing', 'Machine Learning Basics', 'Web Development Guide'],
            'author': ['Dr. Smith', 'Prof. Johnson', 'Tech Team'],
            'publisher': ['Tech Publications', 'Academic Press', 'Online Books'],
            'department': ['Electronics', 'Computer Science', 'Information Technology'],
            'category': ['Reference', 'Textbook', 'Guide'],
            'file_format': ['PDF', 'EPUB', 'PDF'],
            'file_size': ['15MB', '8MB', '12MB']
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Ebooks Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='ebooks_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks/bulk', methods=['POST'])
@jwt_required()
def bulk_create_ebooks():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']

        # Read Excel file
        import pandas as pd
        df = pd.read_excel(file)

        # Validate required columns
        required_columns = ['access_no', 'title', 'author', 'publisher', 'department', 'category', 'file_format', 'file_size']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_ebooks = []
        errors = []

        for index, row in df.iterrows():
            try:
                access_no = str(row['access_no'])

                # Check if ebook already exists
                existing = Ebook.query.filter_by(access_no=access_no).first()
                if existing:
                    errors.append(f"Row {index + 1}: Access number {access_no} already exists")
                    continue

                ebook = Ebook(
                    access_no=access_no,
                    title=row['title'],
                    author=row['author'],
                    publisher=row['publisher'],
                    department=row['department'],
                    category=row['category'],
                    file_format=row['file_format'],
                    file_size=row['file_size']
                )

                db.session.add(ebook)
                created_ebooks.append({
                    'access_no': access_no,
                    'title': row['title'],
                    'author': row['author']
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_ebooks)} e-books',
            'created_ebooks': created_ebooks,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Ebook
@app.route('/api/admin/ebooks/<int:ebook_id>', methods=['PUT'])
@jwt_required()
def update_ebook(ebook_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        ebook = Ebook.query.get(ebook_id)
        if not ebook:
            return jsonify({'error': 'E-book not found'}), 404

        data = request.get_json()
        title = data.get('title')
        author = data.get('author')
        publisher = data.get('publisher')
        category = data.get('category')
        file_format = data.get('file_format')
        file_size = data.get('file_size')

        if not all([title, author]):
            return jsonify({'error': 'Title and author are required'}), 400

        ebook.title = title
        ebook.author = author
        ebook.publisher = publisher
        ebook.category = category
        ebook.format = file_format
        ebook.file_size = file_size

        db.session.commit()

        return jsonify({
            'message': 'E-book updated successfully',
            'ebook': {
                'id': ebook.id,
                'title': ebook.title,
                'author': ebook.author
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Ebook
@app.route('/api/admin/ebooks/<int:ebook_id>', methods=['DELETE'])
@jwt_required()
def delete_ebook(ebook_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        ebook = Ebook.query.get(ebook_id)
        if not ebook:
            return jsonify({'error': 'E-book not found'}), 404

        db.session.delete(ebook)
        db.session.commit()

        return jsonify({'message': 'E-book deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Public E-book endpoints
@app.route('/api/public/ebooks', methods=['GET'])
def get_public_ebooks():
    """Get e-books for public access (students and OPAC)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 12, type=int)
        search = request.args.get('search', '', type=str)
        ebook_type = request.args.get('type', '', type=str)

        query = Ebook.query

        if search:
            query = query.filter(
                db.or_(
                    Ebook.web_title.ilike(f'%{search}%'),
                    Ebook.subject.ilike(f'%{search}%'),
                    Ebook.access_no.ilike(f'%{search}%')
                )
            )

        if ebook_type:
            query = query.filter(Ebook.type.ilike(f'%{ebook_type}%'))

        pagination = query.order_by(Ebook.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        ebooks = []
        for ebook in pagination.items:
            ebooks.append({
                'id': ebook.id,
                'access_no': ebook.access_no,
                'website': ebook.website,
                'web_detail': ebook.web_detail,
                'web_title': ebook.web_title,
                'subject': ebook.subject,
                'type': ebook.type,
                'download_count': ebook.download_count,
                'created_at': ebook.created_at.isoformat()
            })

        return jsonify({
            'ebooks': ebooks,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/public/ebooks/types', methods=['GET'])
def get_ebook_types():
    """Get all unique e-book types for filtering"""
    try:
        types = db.session.query(Ebook.type).distinct().all()
        ebook_types = [t[0] for t in types if t[0]]
        return jsonify({'ebook_types': ebook_types}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ebooks/<int:ebook_id>/visit', methods=['POST'])
@jwt_required()
def visit_ebook(ebook_id):
    """Track e-book visits"""
    try:
        ebook = Ebook.query.get_or_404(ebook_id)

        # Increment download count (using as visit count)
        ebook.download_count += 1
        db.session.commit()

        return jsonify({
            'message': 'Visit tracked',
            'website': ebook.website
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update User
@app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        college_id = data.get('college_id')
        department_id = data.get('department_id')
        validity_date = data.get('validity_date')
        is_active = data.get('is_active', True)

        if not all([name, email]):
            return jsonify({'error': 'Name and email are required'}), 400

        # Check if another user with same email exists
        existing_email = User.query.filter(User.email == email, User.id != user_id).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 400

        user.name = name
        user.email = email
        user.college_id = college_id
        user.department_id = department_id
        user.is_active = is_active

        if validity_date:
            user.validity_date = datetime.strptime(validity_date, '%Y-%m-%d').date()

        db.session.commit()

        return jsonify({
            'message': 'User updated successfully',
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete User
@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user has active circulations
        active_circulations = Circulation.query.filter_by(user_id=user_id, status='issued').count()
        if active_circulations > 0:
            return jsonify({'error': 'Cannot delete user with active book loans'}), 400

        db.session.delete(user)
        db.session.commit()

        return jsonify({'message': 'User deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/')
def index():
    return {'message': 'Library Management System API'}

# Test endpoint for CORS verification (no auth required)
@app.route('/api/test', methods=['GET', 'OPTIONS'])
def test_endpoint():
    if request.method == 'OPTIONS':
        # Handle preflight request
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS')
        return response

    return jsonify({
        'status': 'success',
        'message': 'CORS test endpoint working',
        'timestamp': datetime.utcnow().isoformat()
    }), 200

# ===== QUESTION BANK ENDPOINTS =====

@app.route('/api/admin/question-banks', methods=['GET'])
@jwt_required()
def get_question_banks():
    """Get all question banks with pagination and filtering"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin or librarian access required'}), 403

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '', type=str)
        college_id = request.args.get('college_id', type=int)
        department_id = request.args.get('department_id', type=int)

        # Build query with joins
        query = db.session.query(QuestionBank, College, Department, User).join(
            College, QuestionBank.college_id == College.id
        ).join(
            Department, QuestionBank.department_id == Department.id
        ).join(
            User, QuestionBank.uploaded_by == User.id
        )

        # Apply filters
        if search:
            query = query.filter(
                db.or_(
                    QuestionBank.subject_name.ilike(f'%{search}%'),
                    QuestionBank.subject_code.ilike(f'%{search}%'),
                    QuestionBank.regulation.ilike(f'%{search}%')
                )
            )

        if college_id:
            query = query.filter(QuestionBank.college_id == college_id)

        if department_id:
            query = query.filter(QuestionBank.department_id == department_id)

        # Paginate
        pagination = query.order_by(QuestionBank.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        qb_list = []
        for qb, college, department, uploader in pagination.items:
            qb_data = {
                'id': qb.id,
                'subject_name': qb.subject_name,
                'subject_code': qb.subject_code,
                'regulation': qb.regulation,
                'file_name': qb.file_name,
                'file_size': qb.file_size,
                'download_count': qb.download_count,
                'created_at': qb.created_at.isoformat(),
                'college': {
                    'id': college.id,
                    'name': college.name,
                    'code': college.code
                },
                'department': {
                    'id': department.id,
                    'name': department.name,
                    'code': department.code
                },
                'uploader': {
                    'id': uploader.id,
                    'name': uploader.name,
                    'email': uploader.email
                }
            }
            qb_list.append(qb_data)

        return jsonify({
            'question_banks': qb_list,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/question-banks', methods=['POST'])
@jwt_required()
def create_question_bank():
    """Create a new question bank"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin or librarian access required'}), 403

        # Check if file is uploaded
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type (only PDF)
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        # Get form data
        college_id = request.form.get('college_id', type=int)
        department_id = request.form.get('department_id', type=int)
        subject_name = request.form.get('subject_name', '').strip()
        subject_code = request.form.get('subject_code', '').strip()
        regulation = request.form.get('regulation', '').strip()

        # Validate required fields
        if not all([college_id, department_id, subject_name, subject_code]):
            return jsonify({'error': 'College, Department, Subject Name, and Subject Code are required'}), 400

        # Validate college and department exist
        college = College.query.get(college_id)
        department = Department.query.get(department_id)

        if not college:
            return jsonify({'error': 'Invalid college selected'}), 400

        if not department or department.college_id != college_id:
            return jsonify({'error': 'Invalid department selected for the college'}), 400

        # Create uploads directory if it doesn't exist
        import os
        upload_dir = os.path.join(os.path.dirname(__file__), 'uploads', 'question_banks')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        import uuid
        file_extension = '.pdf'
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save file
        file.save(file_path)

        # Get file size
        file_size = os.path.getsize(file_path)
        file_size_str = f"{file_size / (1024 * 1024):.2f} MB"

        # Create question bank record
        question_bank = QuestionBank(
            college_id=college_id,
            department_id=department_id,
            subject_name=subject_name,
            subject_code=subject_code,
            regulation=regulation if regulation else None,
            file_path=file_path,
            file_name=file.filename,
            file_size=file_size_str,
            uploaded_by=current_user_id
        )

        db.session.add(question_bank)
        db.session.commit()

        return jsonify({
            'message': 'Question bank uploaded successfully',
            'id': question_bank.id
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/question-banks/search', methods=['GET'])
@jwt_required()
def search_question_banks():
    """Search question banks for students and public access"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 12, type=int)
        search = request.args.get('search', '', type=str)
        college_id = request.args.get('college_id', type=int)
        department_id = request.args.get('department_id', type=int)

        # Build query with joins
        query = db.session.query(QuestionBank, College, Department).join(
            College, QuestionBank.college_id == College.id
        ).join(
            Department, QuestionBank.department_id == Department.id
        )

        # Apply filters
        if search:
            query = query.filter(
                db.or_(
                    QuestionBank.subject_name.ilike(f'%{search}%'),
                    QuestionBank.subject_code.ilike(f'%{search}%'),
                    QuestionBank.regulation.ilike(f'%{search}%'),
                    College.name.ilike(f'%{search}%'),
                    Department.name.ilike(f'%{search}%')
                )
            )

        if college_id:
            query = query.filter(QuestionBank.college_id == college_id)

        if department_id:
            query = query.filter(QuestionBank.department_id == department_id)

        # Paginate
        pagination = query.order_by(QuestionBank.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        qb_list = []
        for qb, college, department in pagination.items:
            qb_data = {
                'id': qb.id,
                'subject_name': qb.subject_name,
                'subject_code': qb.subject_code,
                'regulation': qb.regulation,
                'file_name': qb.file_name,
                'file_size': qb.file_size,
                'download_count': qb.download_count,
                'created_at': qb.created_at.isoformat(),
                'college': {
                    'id': college.id,
                    'name': college.name,
                    'code': college.code
                },
                'department': {
                    'id': department.id,
                    'name': department.name,
                    'code': department.code
                }
            }
            qb_list.append(qb_data)

        return jsonify({
            'question_banks': qb_list,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/question-banks/<int:qb_id>/download', methods=['GET'])
@jwt_required()
def download_question_bank(qb_id):
    """Download a question bank file"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get question bank
        qb = QuestionBank.query.get(qb_id)
        if not qb:
            return jsonify({'error': 'Question bank not found'}), 404

        # Check if file exists
        import os
        if not os.path.exists(qb.file_path):
            return jsonify({'error': 'File not found on server'}), 404

        # Increment download count
        qb.download_count += 1
        db.session.commit()

        # Send file
        from flask import send_file
        return send_file(
            qb.file_path,
            as_attachment=True,
            download_name=qb.file_name,
            mimetype='application/pdf'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/question-banks/<int:qb_id>', methods=['DELETE'])
@jwt_required()
def delete_question_bank(qb_id):
    """Delete a question bank"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin or librarian access required'}), 403

        # Get question bank
        qb = QuestionBank.query.get(qb_id)
        if not qb:
            return jsonify({'error': 'Question bank not found'}), 404

        # Delete file from filesystem
        import os
        if os.path.exists(qb.file_path):
            os.remove(qb.file_path)

        # Delete from database
        db.session.delete(qb)
        db.session.commit()

        return jsonify({'message': 'Question bank deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/colleges', methods=['GET'])
@jwt_required()
def get_all_colleges():
    """Get all colleges"""
    try:
        colleges = College.query.all()
        college_list = []
        for college in colleges:
            college_list.append({
                'id': college.id,
                'name': college.name,
                'code': college.code
            })

        return jsonify({'colleges': college_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/colleges/<int:college_id>/departments', methods=['GET'])
@jwt_required()
def get_departments_by_college(college_id):
    """Get departments by college"""
    try:
        departments = Department.query.filter_by(college_id=college_id).all()
        department_list = []
        for department in departments:
            department_list.append({
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            })

        return jsonify({'departments': department_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===== PUBLIC QUESTION BANK ENDPOINTS (NO AUTH REQUIRED) =====

@app.route('/api/public/question-banks/search', methods=['GET'])
def public_search_question_banks():
    """Public search for question banks (no auth required)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 12, type=int)
        search = request.args.get('search', '', type=str)
        college_id = request.args.get('college_id', type=int)
        department_id = request.args.get('department_id', type=int)

        # Build query with joins
        query = db.session.query(QuestionBank, College, Department).join(
            College, QuestionBank.college_id == College.id
        ).join(
            Department, QuestionBank.department_id == Department.id
        )

        # Apply filters
        if search:
            query = query.filter(
                db.or_(
                    QuestionBank.subject_name.ilike(f'%{search}%'),
                    QuestionBank.subject_code.ilike(f'%{search}%'),
                    QuestionBank.regulation.ilike(f'%{search}%'),
                    College.name.ilike(f'%{search}%'),
                    Department.name.ilike(f'%{search}%')
                )
            )

        if college_id:
            query = query.filter(QuestionBank.college_id == college_id)

        if department_id:
            query = query.filter(QuestionBank.department_id == department_id)

        # Paginate
        pagination = query.order_by(QuestionBank.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        qb_list = []
        for qb, college, department in pagination.items:
            qb_data = {
                'id': qb.id,
                'subject_name': qb.subject_name,
                'subject_code': qb.subject_code,
                'regulation': qb.regulation,
                'file_name': qb.file_name,
                'file_size': qb.file_size,
                'download_count': qb.download_count,
                'created_at': qb.created_at.isoformat(),
                'college': {
                    'id': college.id,
                    'name': college.name,
                    'code': college.code
                },
                'department': {
                    'id': department.id,
                    'name': department.name,
                    'code': department.code
                }
            }
            qb_list.append(qb_data)

        return jsonify({
            'question_banks': qb_list,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/public/colleges', methods=['GET'])
def public_get_colleges():
    """Get all colleges (public access)"""
    try:
        colleges = College.query.all()
        college_list = []
        for college in colleges:
            college_list.append({
                'id': college.id,
                'name': college.name,
                'code': college.code
            })

        return jsonify({'colleges': college_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/public/colleges/<int:college_id>/departments', methods=['GET'])
def public_get_departments_by_college(college_id):
    """Get departments by college (public access)"""
    try:
        departments = Department.query.filter_by(college_id=college_id).all()
        department_list = []
        for department in departments:
            department_list.append({
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            })

        return jsonify({'departments': department_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500



# ===== NEWS CLIPPING ENDPOINTS =====

@app.route('/api/admin/news-clippings', methods=['GET'])
@jwt_required()
def get_news_clippings():
    """Get all news clippings with pagination and search"""
    try:
        print("📰 Getting news clippings...")
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '', type=str)
        news_type = request.args.get('news_type', '', type=str)

        print(f"📰 Query params: page={page}, per_page={per_page}, search='{search}', news_type='{news_type}'")

        # Check if NewsClipping table exists
        try:
            query = NewsClipping.query
            print("📰 NewsClipping table exists")
        except Exception as table_error:
            print(f"❌ NewsClipping table error: {table_error}")
            return jsonify({'error': f'NewsClipping table not found: {str(table_error)}'}), 500

        if search:
            query = query.filter(
                db.or_(
                    NewsClipping.news_title.ilike(f'%{search}%'),
                    NewsClipping.newspaper_name.ilike(f'%{search}%'),
                    NewsClipping.news_subject.ilike(f'%{search}%'),
                    NewsClipping.keywords.ilike(f'%{search}%'),
                    NewsClipping.clipping_no.ilike(f'%{search}%')
                )
            )

        if news_type:
            query = query.filter(NewsClipping.news_type.ilike(f'%{news_type}%'))

        print("📰 Executing query...")
        try:
            pagination = query.order_by(NewsClipping.created_at.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )
            print(f"📰 Found {pagination.total} total clippings")
        except Exception as query_error:
            print(f"❌ Query error: {query_error}")
            return jsonify({'error': f'Query failed: {str(query_error)}'}), 500

        clippings = []
        for clipping in pagination.items:
            try:
                clippings.append({
                    'id': clipping.id,
                    'clipping_no': clipping.clipping_no,
                    'newspaper_name': clipping.newspaper_name,
                    'news_type': clipping.news_type,
                    'date': clipping.date.isoformat(),
                    'pages': clipping.pages,
                    'news_title': clipping.news_title,
                    'news_subject': clipping.news_subject,
                    'keywords': clipping.keywords,
                    'pdf_file_name': clipping.pdf_file_name,
                    'pdf_file_size': clipping.pdf_file_size,
                    'abstract': clipping.abstract,
                    'content': clipping.content,
                    'download_count': clipping.download_count,
                    'created_by': clipping.creator.name if clipping.creator else 'Unknown',
                    'created_at': clipping.created_at.isoformat()
                })
            except Exception as item_error:
                print(f"❌ Error processing clipping {clipping.id}: {item_error}")
                continue

        print(f"📰 Returning {len(clippings)} clippings")
        return jsonify({
            'news_clippings': clippings,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        print(f"❌ General error in get_news_clippings: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/news-clippings', methods=['POST'])
@jwt_required()
def create_news_clipping():
    """Create a new news clipping"""
    try:
        current_user_id = get_jwt_identity()

        # Check if file is present
        if 'pdf_file' not in request.files:
            return jsonify({'error': 'PDF file is required'}), 400

        file = request.files['pdf_file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        # Get form data
        clipping_no = request.form.get('clipping_no')
        newspaper_name = request.form.get('newspaper_name')
        news_type = request.form.get('news_type')
        date_str = request.form.get('date')
        pages = request.form.get('pages')
        news_title = request.form.get('news_title')
        news_subject = request.form.get('news_subject')
        keywords = request.form.get('keywords')
        abstract = request.form.get('abstract', '')
        content = request.form.get('content', '')

        # Validate required fields
        required_fields = [clipping_no, newspaper_name, news_type, date_str, pages, news_title, news_subject, keywords]
        if not all(required_fields):
            return jsonify({'error': 'All required fields must be filled'}), 400

        # Check if clipping_no already exists
        existing = NewsClipping.query.filter_by(clipping_no=clipping_no).first()
        if existing:
            return jsonify({'error': 'Clipping number already exists'}), 400

        # Parse date
        try:
            from datetime import datetime
            news_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

        # Save file
        import os
        import uuid

        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(os.path.dirname(__file__), 'uploads', 'news_clippings')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save file
        file.save(file_path)

        # Get file size
        file_size = os.path.getsize(file_path)
        file_size_str = f"{file_size / (1024 * 1024):.2f} MB"

        # Create news clipping record
        news_clipping = NewsClipping(
            clipping_no=clipping_no,
            newspaper_name=newspaper_name,
            news_type=news_type,
            date=news_date,
            pages=pages,
            news_title=news_title,
            news_subject=news_subject,
            keywords=keywords,
            pdf_file_name=file.filename,
            pdf_file_path=file_path,
            pdf_file_size=file_size_str,
            abstract=abstract if abstract else None,
            content=content if content else None,
            created_by=current_user_id
        )

        db.session.add(news_clipping)
        db.session.commit()

        return jsonify({
            'message': 'News clipping created successfully',
            'id': news_clipping.id
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/news-clippings/<int:clipping_id>', methods=['DELETE'])
@jwt_required()
def delete_news_clipping(clipping_id):
    """Delete a news clipping"""
    try:
        clipping = NewsClipping.query.get_or_404(clipping_id)

        # Delete the file
        import os
        if os.path.exists(clipping.pdf_file_path):
            os.remove(clipping.pdf_file_path)

        db.session.delete(clipping)
        db.session.commit()

        return jsonify({'message': 'News clipping deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/news-clippings/<int:clipping_id>/download', methods=['GET'])
@jwt_required()
def download_news_clipping(clipping_id):
    """Download news clipping PDF"""
    try:
        clipping = NewsClipping.query.get_or_404(clipping_id)

        # Increment download count
        clipping.download_count += 1
        db.session.commit()

        # Send file
        from flask import send_file
        return send_file(
            clipping.pdf_file_path,
            as_attachment=True,
            download_name=clipping.pdf_file_name,
            mimetype='application/pdf'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/public/news-clippings', methods=['GET'])
def get_public_news_clippings():
    """Get news clippings for public access (OPAC and student dashboard)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 12, type=int)
        search = request.args.get('search', '', type=str)
        news_type = request.args.get('news_type', '', type=str)

        query = NewsClipping.query

        if search:
            query = query.filter(
                db.or_(
                    NewsClipping.news_title.ilike(f'%{search}%'),
                    NewsClipping.newspaper_name.ilike(f'%{search}%'),
                    NewsClipping.news_subject.ilike(f'%{search}%'),
                    NewsClipping.keywords.ilike(f'%{search}%')
                )
            )

        if news_type:
            query = query.filter(NewsClipping.news_type.ilike(f'%{news_type}%'))

        pagination = query.order_by(NewsClipping.date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        clippings = []
        for clipping in pagination.items:
            clippings.append({
                'id': clipping.id,
                'clipping_no': clipping.clipping_no,
                'newspaper_name': clipping.newspaper_name,
                'news_type': clipping.news_type,
                'date': clipping.date.isoformat(),
                'pages': clipping.pages,
                'news_title': clipping.news_title,
                'news_subject': clipping.news_subject,
                'keywords': clipping.keywords,
                'pdf_file_name': clipping.pdf_file_name,
                'pdf_file_size': clipping.pdf_file_size,
                'abstract': clipping.abstract,
                'download_count': clipping.download_count,
                'created_at': clipping.created_at.isoformat()
            })

        return jsonify({
            'news_clippings': clippings,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/public/news-clippings/types', methods=['GET'])
def get_news_types():
    """Get all unique news types for filtering"""
    try:
        types = db.session.query(NewsClipping.news_type).distinct().all()
        news_types = [t[0] for t in types if t[0]]
        return jsonify({'news_types': news_types}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test/news-clippings', methods=['GET'])
def test_news_clippings():
    """Test endpoint to check news clippings table"""
    try:
        # Test if we can query the table
        count = NewsClipping.query.count()
        return jsonify({
            'status': 'success',
            'message': 'News clippings table is working',
            'count': count
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# Quick setup endpoint to create sample data (no auth required for testing)
@app.route('/api/setup-test-data', methods=['POST'])
def setup_test_data():
    try:
        # Create sample colleges if they don't exist
        colleges_data = [
            {'name': 'ABC Engineering College', 'code': 'ABC'},
            {'name': 'XYZ Institute of Technology', 'code': 'XYZ'},
            {'name': 'PQR University', 'code': 'PQR'}
        ]

        created_colleges = []
        for college_data in colleges_data:
            existing_college = College.query.filter_by(code=college_data['code']).first()
            if not existing_college:
                college = College(name=college_data['name'], code=college_data['code'])
                db.session.add(college)
                db.session.flush()
                created_colleges.append(college)
            else:
                created_colleges.append(existing_college)

        # Create sample departments for each college
        departments_data = [
            {'name': 'Computer Science Engineering', 'code': 'CSE'},
            {'name': 'Information Technology', 'code': 'IT'},
            {'name': 'Electronics and Communication Engineering', 'code': 'ECE'},
            {'name': 'Mechanical Engineering', 'code': 'MECH'},
            {'name': 'Civil Engineering', 'code': 'CIVIL'}
        ]

        created_departments = []
        for college in created_colleges:
            for dept_data in departments_data:
                existing_dept = Department.query.filter_by(
                    code=dept_data['code'],
                    college_id=college.id
                ).first()
                if not existing_dept:
                    department = Department(
                        name=dept_data['name'],
                        code=dept_data['code'],
                        college_id=college.id
                    )
                    db.session.add(department)
                    created_departments.append(department)

        db.session.flush()

        # Create sample students using the first college and department
        first_college = created_colleges[0] if created_colleges else None
        first_department = created_departments[0] if created_departments else None

        if first_college and first_department:
            sample_students = [
                {'user_id': '22', 'name': 'John Doe', 'email': '<EMAIL>'},
                {'user_id': '23', 'name': 'Jane Smith', 'email': '<EMAIL>'},
                {'user_id': '24', 'name': 'Bob Johnson', 'email': '<EMAIL>'},
            ]

            created_students = []
            for student_data in sample_students:
                existing = User.query.filter_by(user_id=student_data['user_id']).first()
                if not existing:
                    student = User(
                        user_id=student_data['user_id'],
                        username=student_data['user_id'],  # Use user_id as username
                        name=student_data['name'],
                        email=student_data['email'],
                        role='student',
                        designation='Student',  # Add required designation
                        dob=datetime(2000, 1, 1).date(),  # Add required date of birth
                        college_id=first_college.id,
                        department_id=first_department.id,
                        validity_date=datetime(2025, 12, 31)
                    )
                    student.set_password('password123')
                    db.session.add(student)
                    created_students.append(student_data['user_id'])

        # Create sample books
        sample_books = [
            {'access_no': 'BK001', 'title': 'Python Programming', 'author': 'John Author', 'category': 'Technology', 'number_of_copies': 3, 'available_copies': 3},
            {'access_no': 'BK002', 'title': 'Web Development', 'author': 'Jane Author', 'category': 'Technology', 'number_of_copies': 2, 'available_copies': 2},
        ]

        created_books = []
        for book_data in sample_books:
            existing = Book.query.filter_by(access_no=book_data['access_no']).first()
            if not existing:
                book = Book(**book_data)
                db.session.add(book)
                created_books.append(book_data['access_no'])

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Test data created successfully',
            'created_colleges': len(created_colleges),
            'created_departments': len(created_departments),
            'created_students': len(created_students) if 'created_students' in locals() else 0,
            'created_books': len(created_books) if 'created_books' in locals() else 0
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Test user search endpoint (no auth required for testing)
@app.route('/api/test-user-search/<user_id>', methods=['GET'])
def test_user_search(user_id):
    try:
        # Find user by user_id (roll number)
        print(f"Searching for user with user_id: {user_id}")
        user = User.query.filter_by(user_id=user_id).first()
        print(f"Found user by user_id: {user}")

        if not user:
            # Debug: Show available users
            all_users = User.query.filter_by(role='student').limit(5).all()
            available_user_ids = [u.user_id for u in all_users]
            print(f"Available student user_ids: {available_user_ids}")
            return jsonify({
                'error': 'User not found',
                'searched_for': user_id,
                'available_student_ids': available_user_ids
            }), 404

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'message': 'User found successfully'
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# List all students endpoint (no auth required for testing)
@app.route('/api/list-students', methods=['GET'])
def list_students():
    try:
        students = User.query.filter_by(role='student').all()
        return jsonify({
            'students': [{
                'id': student.id,
                'user_id': student.user_id,
                'name': student.name,
                'email': student.email
            } for student in students],
            'count': len(students)
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def cleanup_expired_reservations():
    """Cancel reservations that haven't been picked up within 2 days of notification"""
    try:
        from datetime import datetime, timedelta

        # Find reservations that have expired pickup deadline (2 days after pickup date)
        two_days_ago = datetime.utcnow() - timedelta(days=2)
        expired_reservations = Reservation.query.filter(
            Reservation.pickup_deadline.isnot(None),
            Reservation.pickup_deadline < two_days_ago,
            Reservation.status == 'active'
        ).all()

        for reservation in expired_reservations:
            print(f"Cancelling expired reservation {reservation.id} for user {reservation.user_id}")
            reservation.status = 'expired'

            # Update queue positions for other reservations
            other_reservations = Reservation.query.filter(
                Reservation.book_id == reservation.book_id,
                Reservation.status == 'active',
                Reservation.queue_position > reservation.queue_position
            ).all()

            for res in other_reservations:
                res.queue_position -= 1

        # Find reservations that have been active for more than 30 days without notification
        old_reservations = Reservation.query.filter(
            Reservation.notification_date.is_(None),
            Reservation.reservation_date < datetime.utcnow() - timedelta(days=30),
            Reservation.status == 'active'
        ).all()

        for reservation in old_reservations:
            print(f"Cancelling old reservation {reservation.id} for user {reservation.user_id}")
            reservation.status = 'expired'

            # Update queue positions
            other_reservations = Reservation.query.filter(
                Reservation.book_id == reservation.book_id,
                Reservation.status == 'active',
                Reservation.queue_position > reservation.queue_position
            ).all()

            for res in other_reservations:
                res.queue_position -= 1

        if expired_reservations or old_reservations:
            db.session.commit()
            print(f"✅ Cleaned up {len(expired_reservations + old_reservations)} expired reservations")

    except Exception as e:
        print(f"❌ Error cleaning up reservations: {str(e)}")
        db.session.rollback()

def notify_available_reservations():
    """Notify users when their reserved books become available"""
    try:
        from datetime import datetime, timedelta

        # Find books that have become available and have active reservations
        available_books = db.session.query(Book).filter(
            Book.available_copies > 0
        ).all()

        for book in available_books:
            # Get the first person in queue
            next_reservation = Reservation.query.filter_by(
                book_id=book.id,
                status='active',
                queue_position=1,
                notification_date=None
            ).first()

            if next_reservation:
                # Notify the user (set notification date and pickup deadline)
                next_reservation.notification_date = datetime.utcnow()
                next_reservation.pickup_deadline = datetime.utcnow() + timedelta(days=2)

                print(f"📧 Notified user {next_reservation.user_id} that book {book.title} is available")
                print(f"   Pickup deadline: {next_reservation.pickup_deadline}")

        db.session.commit()

    except Exception as e:
        print(f"❌ Error notifying reservations: {str(e)}")
        db.session.rollback()

def run_migrations():
    """Run database migrations to add missing columns"""
    try:
        print("Checking database schema...")

        # Check if fine_amount column exists in circulations table
        from sqlalchemy import inspect
        inspector = inspect(db.engine)

        # Check if circulations table exists and add fine_amount column
        if 'circulations' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('circulations')]

            if 'fine_amount' not in columns:
                print("Adding fine_amount column to circulations table...")
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE circulations ADD COLUMN fine_amount REAL DEFAULT 0.0"))
                    conn.commit()
                print("✅ fine_amount column added successfully!")

        # Check if users table exists and add user_role column
        if 'users' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('users')]

            if 'user_role' not in columns:
                print("Adding user_role column to users table...")
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE users ADD COLUMN user_role VARCHAR(20) DEFAULT 'student'"))
                    conn.commit()
                print("✅ user_role column added successfully!")

        # Check if gate entry tables exist
        table_names = inspector.get_table_names()

        if 'gate_entry_credentials' not in table_names:
            print("Creating gate_entry_credentials table...")
            # Table will be created by db.create_all()

        if 'gate_entry_logs' not in table_names:
            print("Creating gate_entry_logs table...")
            # Table will be created by db.create_all()

        # Check and migrate news_clippings table
        if 'news_clippings' in table_names:
            print("Checking news_clippings table structure...")
            try:
                # Check if the new columns exist
                result = db.session.execute(text("PRAGMA table_info(news_clippings)"))
                columns = [row[1] for row in result.fetchall()]

                required_columns = [
                    'clipping_no', 'newspaper_name', 'news_type', 'date', 'pages',
                    'news_title', 'news_subject', 'keywords', 'pdf_file_name',
                    'pdf_file_path', 'pdf_file_size', 'abstract', 'download_count',
                    'created_by', 'updated_at'
                ]

                missing_columns = [col for col in required_columns if col not in columns]

                if missing_columns:
                    print(f"News clippings table needs migration. Missing columns: {missing_columns}")
                    print("Dropping and recreating news_clippings table...")

                    # Drop the old table
                    db.session.execute(text("DROP TABLE IF EXISTS news_clippings"))
                    db.session.commit()

                    # Create the new table
                    db.create_all()
                    print("✅ News clippings table recreated with new schema")
                else:
                    print("✅ News clippings table is up to date")

            except Exception as e:
                print(f"Error checking news_clippings table: {e}")
                print("Recreating news_clippings table...")
                try:
                    db.session.execute(text("DROP TABLE IF EXISTS news_clippings"))
                    db.session.commit()
                    db.create_all()
                    print("✅ News clippings table recreated")
                except Exception as recreate_error:
                    print(f"Error recreating table: {recreate_error}")

        # Check and migrate ebooks table
        if 'ebooks' in table_names:
            print("Checking ebooks table structure...")
            try:
                # Check if the new columns exist
                result = db.session.execute(text("PRAGMA table_info(ebooks)"))
                columns = [row[1] for row in result.fetchall()]

                required_columns = [
                    'access_no', 'website', 'web_detail', 'web_title', 'subject',
                    'type', 'download_count', 'created_by', 'updated_at'
                ]

                missing_columns = [col for col in required_columns if col not in columns]

                if missing_columns:
                    print(f"Ebooks table needs migration. Missing columns: {missing_columns}")
                    print("Dropping and recreating ebooks table...")

                    # Drop the old table
                    db.session.execute(text("DROP TABLE IF EXISTS ebooks"))
                    db.session.commit()

                    # Create the new table
                    db.create_all()
                    print("✅ Ebooks table recreated with new schema")
                else:
                    print("✅ Ebooks table is up to date")

            except Exception as e:
                print(f"Error checking ebooks table: {e}")
                print("Recreating ebooks table...")
                try:
                    db.session.execute(text("DROP TABLE IF EXISTS ebooks"))
                    db.session.commit()
                    db.create_all()
                    print("✅ Ebooks table recreated")
                except Exception as recreate_error:
                    print(f"Error recreating ebooks table: {recreate_error}")

        print("✅ Database schema is up to date!")

    except Exception as e:
        print(f"❌ Migration error: {e}")
        print("🔧 If this error persists, you may need to reset the database.")
        print("   To reset: Run 'python reset_database.py' or delete 'library.db' and restart.")

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db.session.execute(text('SELECT 1'))
        return jsonify({
            'status': 'healthy',
            'message': 'Server is running and database is connected',
            'timestamp': datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    with app.app_context():
        # Create all tables
        db.create_all()

        # Run migrations for existing databases
        run_migrations()

        # Create default admin user if not exists
        try:
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                print("Creating default admin user...")
                admin_user = User(
                    user_id='ADMIN001',
                    name='System Administrator',
                    email='<EMAIL>',
                    role='admin'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ Default admin user created!")
                print("   Email: <EMAIL>")
                print("   Password: admin123")
        except Exception as e:
            print(f"Admin user creation error: {e}")

        # Run cleanup functions
        print("🔄 Running reservation cleanup...")
        try:
            cleanup_expired_reservations()
            notify_available_reservations()
            print("✅ Cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

    print("🚀 Starting Library Management System...")
    print("📍 Running on localhost only")
    print("🌐 Access at: http://localhost:5173")
    app.run(host='localhost', port=5000, debug=True)

